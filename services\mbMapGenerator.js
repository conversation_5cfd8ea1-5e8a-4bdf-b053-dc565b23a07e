"use strict"
import { Console } from "console";
/*************************
    MB Services Classes
    Coded by <PERSON><PERSON>
**************************/
import md5 from "md5";
var crypto = require("crypto");
const base32Encode = require('base32-encode')

class MBMapGenerator {
    constructor (dbConnector) {
        this.db = dbConnector;

        this.get = this.get.bind(this);
        this.add = this.add.bind(this);
        this.tokenCheck = this.tokenCheck.bind(this); 
        this.tokenAdd = this.tokenAdd.bind(this); 
        this.tokenConfirm = this.tokenConfirm.bind(this); 

        this.generateMapCode = this.generateMapCode.bind(this);         
    }    
    
    getMaps(HU,callback) {
        let _this = this;      
        let SQLquery = { query: "CALL mapcodetypeGet(?);" , params: [ HU ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            if (result !==null) {
                callback(result[0]);
            } else {
                callback(null);
            }
        });            
    }

    tokenCheck(userid,cid,sku,hwid,swid,callback) {
        let _this = this;        
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenCheck(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, md5(cid+sku+hwid+swid), sku ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });    
    }
    
    tokenAdd(userid,cid,sku,hwid,swid,state,callback) {
        let _this = this;
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenAdd(?,?,?,?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, md5(cid+sku+hwid+swid), cid, sku, hwid, swid, state ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });    
    }

    tokenConfirm(userid,cid,sku,hwid,swid,state,callback) {
        let _this = this;
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenConfirm(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, md5(cid+sku+hwid+swid), state ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });   
    }    
    
    add(userid, HU, vin, swid, callback) {
        let _this = this;      
        let SQLquery = { query: "CALL mapcodeAdd(?,?,?,?);" , params: [ userid, HU, vin, swid ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            if (result !==null) {
                callback(result[0]);
            } else {
                callback(null);
            }
        });    
    }
    
    get(userid, vin, swid ,callback) {
        let _this = this;
        let SQLquery = { query: "CALL mapcodeGet(?,?,?);" , params: [ userid, vin, swid ] };        
        _this.db.QueryExec(SQLquery, (result)=>{ 
            if (result !==null) {
                callback(result[0]);
            } else {
                callback(null);
            }
        });    
    }

    
    generateMapCode(userid, HU, swid, vin, callback)
    {        
        let resultFromGen = {
            vin: "",
            swid: "",
            mapcode: "",
            hu: "",
            state: 0,
            error: ""
        }
        
        this.get(userid,vin,swid,(dbresult)=>{       
            if (dbresult!=null && dbresult[0]!=null) {
                // Send Existing Data from DB                
                resultFromGen.vin = dbresult[0].vin;
                resultFromGen.swid = dbresult[0].swid;
                resultFromGen.mapcode = dbresult[0].mapcode;
                resultFromGen.hu = dbresult[0].hu;
                resultFromGen.state = dbresult[0].state;
                callback(resultFromGen);
            } else {
                // Added to DB for Generation Code
                this.add(userid, HU, vin, swid, (dbresult)=>{                   
                    resultFromGen.vin = dbresult[0].vin;
                    resultFromGen.swid = dbresult[0].swid;
                    resultFromGen.mapcode = dbresult[0].mapcode;
                    resultFromGen.hu = dbresult[0].hu;
                    resultFromGen.state = dbresult[0].state;
                    console.log("add from gen",resultFromGen);
                    callback(resultFromGen);
                });   
            }
        });        

    }

    createTokenId(vin) {        
        return md5(vin);
    }


}
export default MBMapGenerator;
