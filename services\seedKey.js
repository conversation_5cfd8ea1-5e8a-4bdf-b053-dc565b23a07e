"use strict"
/*************************
    SeedKey Analyzer Class
    Coded by <PERSON><PERSON>
**************************/
const fs = require('fs');
const path = require('path');
const Axios = require('axios');
const execa = require('execa');
const crypto = require('crypto');
import CryptoJS from 'crypto-js';

class SeedKey {
    
    constructor (dbConnector, taskManager) {        
        this.db = dbConnector;    
        this.taskManager = taskManager;

        this.seedkeylibs = process.env.SEEDKEY_LIBS;            
        this.seedkeyExe = path.join(__dirname, process.env.SEEDKEY_EXE);            
        this.seedkeyIC204Exe = process.env.SEEDKEYIC204_EXE;            
        this.seedkeyIC172Py = process.env.SEEDKEYIC172_PY;            
        this.seedkeyEZS213Java = process.env.SEEDKEYEZS213_JAVA;            
        this.seedkeyEZS167Java = process.env.SEEDKEYEZS167_JAVA;            
        this.seedkeyIC177Java = process.env.SEEDKEYIC177_JAVA;      
        this.seedkeySPC213Java = process.env.SEEDKEYSPC213_JAVA;
        this.seedkeyEIS447Java = process.env.SEEDKEYEIS447_JAVA;            

        this.analyze = this.analyze.bind(this); 
        this.list = this.list.bind(this); 
        this.calcseedKey = this.calcseedKey.bind(this);  
        this.Aes128 = this.Aes128.bind(this);  
        this.XorHexString = this.XorHexString.bind(this);     
        this.calcseedKeyLevel4 = this.calcseedKeyLevel4.bind(this);
    }

    analyze(callback) {  
        const _this = this;      
        if (!fs.existsSync(this.seedkeyExe)) {
            callback({ 
                "code" : 404, 
                "payload" : {
                    "title": "SeedKey Analyzer", 
                    "message": "SeedKey File Not Found!"
                }});
            return;
        }

        try {
            const directoryPath = this.seedkeylibs;
            //passsing directoryPath and callback function
            fs.readdir(directoryPath, function (err, files) {
                //handling error
                if (err) {
                    return console.log('Unable to scan directory: ' + err);
                } 
                //listing all files using forEach
                files.forEach(function (file) {
                    // Do whatever you want to do with the file
                    console.log(file); 
                    
                    (async () => {
                        const {stdout} = await execa(_this.seedkeyExe, [_this.seedkeylibs+"\\"+file , '0' , '0000']);
                        let inputVariables = JSON.parse(stdout);
                        console.log(inputVariables);
                        _this.seedKeyAdd(
                            file,
                            inputVariables["ecuname"],
                            inputVariables["levels"].join(","),
                            inputVariables["seedlen"].join(","),
                            inputVariables["keylen"].join(","),
                            (result)=>{
                        
                        });                        
                    })();
                });
            });            
        } catch (error) {
            console.log(error);
        }

        callback({ 
            "code" : 200, 
            "payload" : "Completed"                     
        });
    }   
    
    list(callback) {   
       let SQLquery = { query: "CALL seedkeyList()" , params: [] };
       this.db.QueryExec(SQLquery, (result)=>{    
           if(result !== null) {
               callback(result[0]);
           }
       });        
       //callback([]);
    }    

    calcseedKey(filename,classname,level,seed,callback) {
        const _this = this;      
        (async () => {
            try {
                console.log(filename , level , seed);                
                let {stdout} = await execa(process.env.JAVA_RUNTIME, ['-cp', process.env.SEEDKEYEZS213_PATH, "Unlock_SeedKey", process.env.SEEDKEYEZS213_PATH+"/libs/"+filename , classname , level , seed]);
                let inputVariables = JSON.parse(stdout);
                stdout = stdout.replace(/,  ]/g,"]").replace(/, ]/g,"]");                
                console.log(inputVariables);
                callback(_this.encrypt(JSON.stringify(inputVariables)));                    
            } catch (error) {
                callback({'error':error.code});
                console.log(error);
            }
        })();
    }

    calcseedKeyIC204(swid,level,seed,callback) {
        switch(level) {
            case "2709": level = "1"; break;
            case "270D": level = "2"; break;
            case "2762": level = "3"; break;
            case "2772": level = "71"; break; // IC172 2771
            case "277D": level = "7D"; break; // IC172 270D
            case "2773": level = "7D"; break; // IC172 2703
        }

        const _this = this;      
        (async () => {
            try {
                console.log("Request From Client:",swid , level , seed); 
/*
    IC907 için swap edilmiş seed ve key
    9079023000
    9079023801
    9079026702
    9079028101
    9079020203
    9079022900
    9079023701
    9079026602
    9079028001
    9079027202
*/
                if (swid=="2139026613" || 
                    swid=="9079023000" || 
                    swid=="9079023801" || 
                    swid=="9079026702" || 
                    swid=="9079028101" || 
                    swid=="9079020203" || 
                    swid=="9079022900" || 
                    swid=="9079023701" || 
                    swid=="9079026602" || 
                    swid=="9079028001" || 
                    swid=="9079027202") { 
                    callback(_this.encrypt(JSON.stringify(
                        {"ver":"SGKT Coded By AstronBnX (Reha Bicer)", 
                        "ecuname":"IC204 IC213 IC222", "result" : "Ok",
                        "seed" : [], 
                        "key" : [],
                        "levels" : [9, 13], 
                        "seedlen" : [8, 8], 
                        "keylen" : [8, 8], 
                        "keystr" : seed.substr(14, 2)+seed.substr(12, 2)+seed.substr(10, 2)+seed.substr(8, 2)+seed.substr(6, 2)+seed.substr(4, 2)+seed.substr(2, 2)+seed.substr(0, 2)
                        }))
                    );

                } else
                if (level=="2904") { // For IC213 LEvel4
                    _this.calcseedKeyLevel4(swid,level,seed,callback);
                } else            
                if (swid=="EZS213") {
                    let {stdout} = await execa(process.env.JAVA_RUNTIME, ['-cp', process.env.SEEDKEYEZS213_PATH , _this.seedkeyEZS213Java , level , seed]);
					stdout = stdout.replace(/,  ]/g,"]").replace(/, ]/g,"]");                
					let inputVariables = JSON.parse(stdout);
					console.log(inputVariables);                
                    callback(_this.encrypt(JSON.stringify(inputVariables)));                    
				} else
                if (swid=="EZS167") {
                    let {stdout} = await execa(process.env.JAVA_RUNTIME, ['-cp', process.env.SEEDKEYEZS213_PATH , _this.seedkeyEZS167Java , level , seed]);
					stdout = stdout.replace(/,  ]/g,"]").replace(/, ]/g,"]");                
					let inputVariables = JSON.parse(stdout);
					console.log(inputVariables);                
                    callback(_this.encrypt(JSON.stringify(inputVariables)));                    
				} else
                if (swid=="EIS447") {
                    let {stdout} = await execa(process.env.JAVA_RUNTIME, ['-cp', process.env.SEEDKEYEZS213_PATH , _this.seedkeyEIS447Java , level , seed]);
					stdout = stdout.replace(/,  ]/g,"]").replace(/, ]/g,"]");                
					let inputVariables = JSON.parse(stdout);
					console.log(inputVariables);                
                    callback(_this.encrypt(JSON.stringify(inputVariables)));                    
				} else
                if (swid=="IC177") {
                    let {stdout} = await execa(process.env.JAVA_RUNTIME, ['-cp', process.env.SEEDKEYEZS213_PATH , _this.seedkeyIC177Java , level , seed]);
					stdout = stdout.replace(/,  ]/g,"]").replace(/, ]/g,"]");                
					let inputVariables = JSON.parse(stdout);
					console.log(inputVariables);                
                    callback(_this.encrypt(JSON.stringify(inputVariables)));                    
				} else
                if (swid=="SPC213") {
                    let {stdout} = await execa(process.env.JAVA_RUNTIME, ['-cp', process.env.SEEDKEYEZS213_PATH , _this.seedkeySPC213Java , level , seed]);
					stdout = stdout.replace(/,  ]/g,"]").replace(/, ]/g,"]");                
					let inputVariables = JSON.parse(stdout);
					console.log(inputVariables);                
                    callback(_this.encrypt(JSON.stringify(inputVariables)));                    
				} else
                if (level=="71" || level=="7D") { // For IC172
                    let {stdout} = await execa("python3", [_this.seedkeyIC172Py , level , seed]);
					stdout = stdout.replace(/,  ]/g,"]").replace(/, ]/g,"]");                
					let inputVariables = JSON.parse(stdout);
					inputVariables["keystr"] = inputVariables["keystr"].replace(/(.{2})/g,"$1 ").trim();
					console.log(inputVariables);                
                    callback(_this.encrypt(JSON.stringify(inputVariables)));                    
                } else
                if (level=="2701" || level=="27FD" || level=="2705") { //for KI221
                    switch(level) {
                        case "2701": level = "1"; break;
                        case "2705": level = "5"; break;
                        case "27FD": level = "FD"; break;
                    }            
                    let {stdout} = await execa(_this.seedkeyIC204Exe, [swid , level , seed, "221"]);
					stdout = stdout.replace(/,  ]/g,"]").replace(/, ]/g,"]");                
					let inputVariables = JSON.parse(stdout);
					inputVariables["keystr"] = inputVariables["keystr"].replace(/(.{2})/g,"$1 ").trim();
					console.log(inputVariables);                
                    callback(_this.encrypt(JSON.stringify(inputVariables)));                    
                } else
                if (level=="31FB10") { //for KI211 last 2 chars for magic key level like 77
                    let {stdout} = await execa(_this.seedkeyIC204Exe, [swid , seed.substring(16,18) , seed.substring(0,16), "211"]);
					stdout = stdout.replace(/,  ]/g,"]").replace(/, ]/g,"]");                
					let inputVariables = JSON.parse(stdout);
					inputVariables["keystr"] = inputVariables["keystr"].replace(/(.{2})/g,"$1 ").trim();
					console.log(inputVariables);                
                    callback(_this.encrypt(JSON.stringify(inputVariables)));                    
                } else
                if (level=="31FB01") { //for KI164 last 2 chars for magic key level like 77
                    let {stdout} = await execa(_this.seedkeyIC204Exe, [swid , seed.substring(16,18) , seed.substring(0,16), "164"]);
					stdout = stdout.replace(/,  ]/g,"]").replace(/, ]/g,"]");                
					let inputVariables = JSON.parse(stdout);
					inputVariables["keystr"] = inputVariables["keystr"].replace(/(.{2})/g,"$1 ").trim();
					console.log(inputVariables);                
                    callback(_this.encrypt(JSON.stringify(inputVariables)));                    
                } else
                if (level=="31FB02") { //for 203
                    console.log(swid , seed, "203");
                    let {stdout} = await execa(_this.seedkeyIC204Exe, [swid , seed.substring(8,12) , seed.substring(0,8), "203"]);
					stdout = stdout.replace(/,  ]/g,"]").replace(/, ]/g,"]");                
					let inputVariables = JSON.parse(stdout);
					inputVariables["keystr"] = inputVariables["keystr"].replace(/(.{2})/g,"$1 ").trim();
					console.log(inputVariables);                
                    callback(_this.encrypt(JSON.stringify(inputVariables)));                    
                } else { // For IC204, IC222, IC213
                    let {stdout} = await execa(_this.seedkeyIC204Exe, [swid , level , seed]);
					stdout = stdout.replace(/,  ]/g,"]").replace(/, ]/g,"]");                
					let inputVariables = JSON.parse(stdout);
					inputVariables["keystr"] = inputVariables["keystr"].replace(/(.{2})/g,"$1 ").trim();
					console.log(inputVariables);                
                    callback(_this.encrypt(JSON.stringify(inputVariables)));                    
                }     
            } catch (error) {
                callback({'error':error.code});
                console.log(error);
            }
        })();
    }

    calcseedKeyLevel4(swid,level,input,callback) {
        /*
        31 01 F0 29 01
        71 01 F0 29 01 - static aes key
        71 01 F0 29 02 - eeprom aes key .
        71 01 F0 29 07 - eeprom writeable .
        */

        const _this = this;      
        let xor_mask = "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF";

        //var input = "33 FF CF C1 76 73 80 A1 02 00 20 00 06 00 3F 00 23 03 17 07 83 80 91";
        var seed = input.split("|")[1];        
        var key  = "E5 1A 7F F0 66 81 6F 04 8C F3 5E 4A DA EE 65 1A";

        var JumpValue = 2;

        //if (input.split("|")[0].replace(/\s/g,'').substring(JumpValue*2,JumpValue*2+6).toUpperCase()!="FFFFFF" && input.split("|")[2]=="02") {
        if (input.split("|")[2]=="02") {
            key = input.split("|")[0].substring(JumpValue*2,JumpValue*2+16*2); //Jump 2 byte            
        }
        console.log("Key: ",key);
        try {
            var keyBuffer = Buffer.from(key.replace(/\s/g,'').substring(0,16*2).toString(16), 'hex');
            var firstpart = seed.replace(/\s/g,'').substring(0,16*2);
            var secondpart = seed.replace(/\s/g,'').substring(16*2,seed.length)+"FFFFFFFFFFFFFFFF01";
            
            var result1 = this.Aes128(this.XorHexString(firstpart,xor_mask),keyBuffer);
            var result2 = this.Aes128(this.XorHexString(result1,secondpart),keyBuffer);
            var result = (result1+result2).toUpperCase().match(/.{1,2}/g).join(' ');
            //console.log(result);
            var inputVariables = {
                "ecuname":"IC213",
                "levels": [4],
                "seed": seed,
                "keystr": result,
                "result": "Ok"
            };
            console.log("Level4 Response: ",inputVariables);
            callback(_this.encrypt(JSON.stringify(inputVariables)));                    
        } catch (error) {
            callback({'level4 error':error.code});
            console.log(error);
        }
    }

    seedKeyAdd(vFileName,vEcuName,vEcuLevels,vSeedLen,vKeyLen, callback) {
        let SQLquery = { query: "CALL SeedKeyAdd(?,?,?,?,?)" , params: [vFileName,vEcuName,vEcuLevels,vSeedLen,vKeyLen] };
        this.db.QueryExec(SQLquery, (result)=>{ 
            if(result !== null && result.affectedRows==1) {
                callback(result);
            }
        });        
    }    

    Aes128(buffer,key) {
        var _buffer = Buffer.from(buffer,'hex');
        let cipher = crypto.createCipheriv("aes-128-ecb", key, '');
        cipher.setAutoPadding(false);
        var result = cipher.update(_buffer).toString('hex');
        result += cipher.final().toString('hex');
        return result;
    }
    
    XorHexString(key, mask) {
        var result = ''
        for (let index = 0; index < key.length; index++) {
          const temp = (parseInt(key.charAt(index), 16) ^ parseInt(mask.charAt(index), 16)).toString(16).toUpperCase()
          result += temp
        }
        return result
    }    
       
    encrypt(plaintext) {
        var key = CryptoJS.enc.Utf8.parse('Pw7dxZs8glfor9tnc2VnsdgH1etkfjcv');
        var iv = CryptoJS.enc.Utf8.parse('bF1lc3sjd8zpt9cx');
        var message = CryptoJS.enc.Utf8.parse(plaintext);
        var encrypted = CryptoJS.AES.encrypt(message, key,        
        {
            keySize: 256,
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });        
        return encrypted.toString();
    }    
}
export default SeedKey;    