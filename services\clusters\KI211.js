"use strict"
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/
import moment from "moment"
import axios from "axios";
import md5 from "md5";
import MBClusterManagerBase from "../mbClusterManagerBase";
import utils from "../../utils";

class KI211 extends MBClusterManagerBase {
    constructor (dbConnector) {     
        super(dbConnector);   

    }

    AMGEnable(swid) {
        let CMDResponse;

        if (swid.startsWith("209")) {
            CMDResponse = {
                workflow: [
                    { proc: "clear" },
                    { proc: "hardreset" },
                    { proc: "sleep", param: 3000 },
                    { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFA, 0x78, 0x00], reply: [0x71, 0xFA] },
                    { proc: "sleep", param: 300 },
                    { proc: "sgkt", param: ["levelver"], reply: [] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                    { proc: "sleep", param: 300 },
                    { proc: "sgkt", param: ["level10"], reply: [] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                    { proc: "sleep", param: 1000 },
                    { proc: "uds", param: [0x3D, 0x39, 0x03, 0xF0, 0x01, 0xFF], reply: [0x7D, 0x07] },
                    { proc: "sleep", param: 1000 },
                    { proc: "hardreset" }
                ]
            };
        } else {
            CMDResponse = {
                workflow: [
                    { proc: "clear" },
                    { proc: "hardreset" },
                    { proc: "sleep", param: 3000 },
                    { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFA, 0x78, 0x00], reply: [0x71, 0xFA] },
                    { proc: "sleep", param: 300 },
                    { proc: "sgkt", param: ["levelver"], reply: [] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                    { proc: "sleep", param: 300 },
                    { proc: "sgkt", param: ["level10"], reply: [] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                    { proc: "sleep", param: 1000 },
                    { proc: "uds", param: [0x23, 0x39, 0x02, 0x10, 0x10], reply: [0x63] },
                    { proc: "sleep", param: 1000 },
                    { proc: "uds", param: [0x3D, 0x39, 0x02, 0x12, 0x02, 0xF9, 0x03], reply: [0x7D, 0x39] },
                    { proc: "sleep", param: 2000 },
                    { proc: "hardreset" }
                ]
            };
        }

        return JSON.stringify(CMDResponse);
    }

    AMGDisable(swid) {        
        let CMDResponse;

        if (swid.startsWith("209")) {
            CMDResponse = {
                workflow: [
                    { proc: "clear" },
                    { proc: "hardreset" },
                    { proc: "sleep", param: 3000 },
                    { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFA, 0x78, 0x00], reply: [0x71, 0xFA] },
                    { proc: "sleep", param: 300 },
                    { proc: "sgkt", param: ["levelver"], reply: [] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                    { proc: "sleep", param: 300 },
                    { proc: "sgkt", param: ["level10"], reply: [] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                    { proc: "sleep", param: 1000 },
                    { proc: "uds", param: [0x3D, 0x39, 0x03, 0xF0, 0x01, 0x06], reply: [0x7D, 0x39] },
                    { proc: "sleep", param: 1000 },
                    { proc: "hardreset" }
                ]
            };
        } else {
            CMDResponse = {
                workflow: [
                    { proc: "clear" },
                    { proc: "hardreset" },
                    { proc: "sleep", param: 3000 },
                    { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFA, 0x78, 0x00], reply: [0x71, 0xFA] },
                    { proc: "sleep", param: 300 },
                    { proc: "sgkt", param: ["levelver"], reply: [] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                    { proc: "sleep", param: 300 },
                    { proc: "sgkt", param: ["level10"], reply: [] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                    { proc: "sleep", param: 500 },
                    { proc: "uds", param: [0x23, 0x39, 0x02, 0x10, 0x10], reply: [0x63] },
                    { proc: "sleep", param: 500 },
                    { proc: "uds", param: [0x3D, 0x39, 0x02, 0x12, 0x02, 0x19, 0x00], reply: [0x7D, 0x07] },
                    { proc: "sleep", param: 1000 },
                    { proc: "hardreset" }
                ]
            };
        }
        return JSON.stringify(CMDResponse);
    }

    Virgin(swid) {
        let CMDResponse;

        if (swid.startsWith("209")) {
            CMDResponse = {
                workflow: [
                    { proc: "clear" },
                    { proc: "hardreset" },
                    { proc: "sleep", param: 3000 },
                    { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFA, 0x78, 0x00], reply: [0x71, 0xFA] },
                    { proc: "sleep", param: 300 },
                    { proc: "sgkt", param: ["levelver"], reply: [] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                    { proc: "sleep", param: 300 },
                    { proc: "sgkt", param: ["level10"], reply: [] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                    { proc: "sleep", param: 1000 },
                    { proc: "uds", param: [0x3D, 0x39, 0x00, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x39] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x3D, 0x39, 0x10, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x39] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x3D, 0x39, 0x20, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x39] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x3D, 0x39, 0x30, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x39] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x3D, 0x39, 0x40, 0x10, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00], reply: [0x7D, 0x39] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x3D, 0x39, 0x50, 0x0E, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x39] },
                    { proc: "sleep", param: 1000 },
                    { proc: "hardreset" },
                    { proc: "sleep", param: 3000 }
                ]
            };
        } else {
            CMDResponse = {
                workflow: [
                    { proc: "clear" },
                    { proc: "hardreset" },
                    { proc: "sleep", param: 3000 },
                    { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFA, 0x78, 0x00], reply: [0x71, 0xFA] },
                    { proc: "sleep", param: 300 },
                    { proc: "sgkt", param: ["levelver"], reply: [] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                    { proc: "sleep", param: 300 },
                    { proc: "sgkt", param: ["level10"], reply: [] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                    { proc: "sleep", param: 1000 },
                    { proc: "uds", param: [0x23, 0x39, 0x00, 0x00, 0x10], reply: [0x63] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x3D, 0x39, 0x00, 0x00, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x23, 0x39, 0x00, 0x10, 0x10], reply: [0x63] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x3D, 0x39, 0x00, 0x10, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x23, 0x39, 0x00, 0x20, 0x10], reply: [0x63] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x3D, 0x39, 0x00, 0x20, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x23, 0x39, 0x00, 0x30, 0x10], reply: [0x63] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x3D, 0x39, 0x00, 0x30, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x23, 0x39, 0x00, 0x40, 0x10], reply: [0x63] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x3D, 0x39, 0x00, 0x40, 0x10, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x23, 0x39, 0x00, 0x50, 0x10], reply: [0x63] },
                    { proc: "sleep", param: 300 },
                    { proc: "uds", param: [0x3D, 0x39, 0x00, 0x50, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00], reply: [0x7D] },
                    { proc: "sleep", param: 1000 },
                    { proc: "hardreset" },
                    { proc: "sleep", param: 3000 }
                ]
            };
        }
        return JSON.stringify(CMDResponse);
    }

    EpromEdit() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 3000 },
                { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] },
                { proc: "sleep", param: 300 },
                { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] },
                { proc: "sleep", param: 300 },
                { proc: "uds", param: [0x31, 0xFA, 0x78, 0x00], reply: [0x71, 0xFA] },
                { proc: "sleep", param: 300 },
                { proc: "sgkt", param: ["levelver"], reply: [] },
                { proc: "sleep", param: 300 },
                { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                { proc: "sleep", param: 300 },
                { proc: "sgkt", param: ["level10"], reply: [] },
                { proc: "sleep", param: 300 },
                { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                { proc: "sleep", param: 1000 }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    Unlock() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 3000 },
                { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] },
                { proc: "sleep", param: 300 },
                { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] },
                { proc: "sleep", param: 300 },
                { proc: "uds", param: [0x31, 0xFA, 0x78, 0x00], reply: [0x71, 0xFA] },
                { proc: "sleep", param: 300 },
                { proc: "sgkt", param: ["levelver"], reply: [] },
                { proc: "sleep", param: 300 },
                { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                { proc: "sleep", param: 300 },
                { proc: "sgkt", param: ["level10"], reply: [] },
                { proc: "sleep", param: 300 },
                { proc: "uds", param: [0x31, 0xFB, 0x11], reply: [0x71, 0xFB] },
                { proc: "sleep", param: 1000 }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

}

export default KI211;
