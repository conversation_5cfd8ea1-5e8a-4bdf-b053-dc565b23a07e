"use strict";
/*************************
    Bank Abstract Class
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _moment = require("moment");

var _moment2 = _interopRequireDefault(_moment);

var _axios = require("axios");

var _axios2 = _interopRequireDefault(_axios);

var _utils = require("../utils");

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var BaseService = function () {
    function BaseService(dbConnector) {
        _classCallCheck(this, BaseService);

        this.db = dbConnector;

        if (this.constructor === BaseService) {
            throw new TypeError("Cannot construct abstract class.");
        }

        if (this.accountActivities === BaseService.prototype.accountActivities) {
            throw new TypeError("Please implement abstract method accountActivities.");
        }
    }

    _createClass(BaseService, [{
        key: "updateTask",
        value: function updateTask(taskItem, state) {
            var SQLquery = { query: "CALL tsk_UpdateAJob(?,?,?,?)", params: [taskItem.payload.id, state, taskItem.taskId, taskItem.payload.lastamount] };
            this.db.QueryExec(SQLquery, function (result) {
                if (result === null) {
                    console.log("Task Update Error:", taskItem.taskId);
                } else {
                    //console.log("Task Update:",result);
                    if (result.affectedRows == 1) {
                        console.log("Task done:", taskItem.taskId);
                    }
                }
            });
        }
    }, {
        key: "updateTransactions",
        value: function updateTransactions(taskItem, data, accountHistory) {
            console.log("Task Activity Writing DB:", taskItem.taskId);
            taskItem.setProcess("Bank transaction list writing to DB");
            var SQLquery = { query: "CALL tsk_PutTransactions(?,?,?,?,?)", params: [taskItem.payload.companyid, taskItem.payload.bankid, taskItem.payload.id, taskItem.payload.bankbranchid, JSON.stringify(data)] };
            this.db.QueryExec(SQLquery, function (result) {
                //console.log("Task Update:",result);               
                if (result === null) {
                    taskItem.setProcess("Bank transaction list writing error");
                    console.log("Task Activity Error:", taskItem.taskId);
                }
                accountHistory(taskItem);
            });
        }
    }, {
        key: "accountActivities",
        value: function accountActivities(taskItem, getActivities) {
            var _this = this;

            taskItem.setWorker(function () {
                try {
                    // Take a Job
                    console.log("Task Get Running:", taskItem.taskId);
                    taskItem.setProcess("Getting a job");

                    var SQLquery = { query: "CALL tsk_GetAJob(?,?)", params: [_this.bankid, taskItem.taskId] };
                    _this.db.QueryExec(SQLquery, function (result) {
                        try {
                            if (result === null) {
                                console.log("Task Get Error:", taskItem.taskId);
                                taskItem.setProcess("Job error");
                                taskItem.setRunning(false);
                            } else {
                                if (result) {
                                    var payload = result[0][0];
                                    var accountPayload = JSON.parse(payload.accpayload);
                                    payload.accpayload = accountPayload;

                                    var bankApi = JSON.parse((0, _utils.decodeAPI)(payload.companyid, payload.bankid, payload.api));
                                    payload.api = bankApi;

                                    taskItem.setPayload(payload);
                                    getActivities(taskItem);
                                } else {
                                    taskItem.setProcess("No job");
                                    console.log("Task done:", taskItem.taskId);
                                    taskItem.setRunning(false);
                                }
                            }
                        } catch (e) {
                            taskItem.setRunning(false);
                            console.log({
                                "taskid": taskItem.taskId,
                                "error": e
                            });
                        }
                    });
                } catch (e) {
                    taskItem.setRunning(false);
                    console.log({
                        "taskid": taskItem.taskId,
                        "error": e
                    });
                }
            });
        }
    }]);

    return BaseService;
}();

exports.default = BaseService;