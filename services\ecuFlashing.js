"use strict"
/*************************
    Seed<PERSON>ey Analyzer Class
    Coded by <PERSON><PERSON>
**************************/
const fs = require('fs');
const path = require('path');
const Axios = require('axios');
const execa = require('execa');
const crypto = require('crypto');
import CryptoJS from 'crypto-js';

class ECUFlasing {
    
    constructor (dbConnector) {        
        this.db = dbConnector;    
    }

    getMagicNumber(name, variant, callback) {
        let response = {};

        // 41C64E6D , 84a7f399
        if (name=="IC222") {
            /*
            switch(variant)
            {
                case "2229041300":
                    response = ["41C64E6D", "16088F1D"]; //IC222_14_28_01
                    break;
                case "2229048700":
                    response = ["41C64E6D", "AF8A3FE9"]; //IC222_13_10_00
                    break;
                case "2229049400":
                    response = ["41C64E6D", "12345678"]; //IC222_12_16_00
                    break;
                case "2229046800":
                    response = ["41C64E6D", "84A7F399"]; //IC222_12_25_00
                    break;
            }  
            */
            
            switch(variant)
            {
                case "32E2":
                    response = ["41C64E6D", "16088F1D"]; //IC222_14_28_01
                    break;
                case "2CE0":
                case "2CE1":
                    response = ["41C64E6D", "AF8A3FE9"]; //IC222_13_10_00
                    break;
                case "ABE1":
                case "ABE2":
                case "ABE3":
                case "ABE6":
                case "ACE1":
                case "ACE3":
                case "ACE4":
                case "ACE5":
                case "B2E0":
                case "B2E1":
                case "B2E2":
                    response = ["41C64E6D", "12345678"]; //IC222_12_16_00
                    break;
                case "2BE0":
                case "2BE3":
                case "2BE4":
                case "32E1":
                    response = ["41C64E6D", "84A7F399"]; //IC222_12_25_00
                    break;
            }  
                 
        }
        console.log(response);
        callback(this.encrypt( JSON.stringify({"keystr":response}) ));
    }

    encrypt(plaintext) {
        var key = CryptoJS.enc.Utf8.parse('Pw7dxZs8glfor9tnc2VnsdgH1etkfjcv');
        var iv = CryptoJS.enc.Utf8.parse('bF1lc3sjd8zpt9cx');
        var message = CryptoJS.enc.Utf8.parse(plaintext);
        var encrypted = CryptoJS.AES.encrypt(message, key,        
        {
            keySize: 256,
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });        
        return encrypted.toString();
    }    
}
export default ECUFlasing;        