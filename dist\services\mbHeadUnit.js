"use strict";
/*************************
    MB Services Classes
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var MBHeadUnit = function () {
    function MBHeadUnit(dbConnector) {
        _classCallCheck(this, MBHeadUnit);

        this.db = dbConnector;

        this.createTokenId = this.createTokenId.bind(this);
    }

    _createClass(MBHeadUnit, [{
        key: "createTokenId",
        value: function createTokenId(hwid, swid) {
            return (0, _md2.default)(hwid + swid);
        }
    }, {
        key: "CheckCarplay",
        value: function CheckCarplay(sku, hwid, callback) {
            var _this = this;
            var SQLquery = { query: "CALL headunitCheck(?,?);", params: [sku, hwid] };
            _this.db.QueryExec(SQLquery, function (result) {
                //console.log(result);
                if (result[0].length > 0) {
                    callback(result[0]);
                }
            });
        }
    }]);

    return MBHeadUnit;
}();

exports.default = MBHeadUnit;