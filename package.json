{"name": "mblink-cluster-service", "version": "1.0.0", "description": "MbLink Cluster Service", "main": "app.js", "dependencies": {"@paypal/checkout-server-sdk": "^1.0.2", "adm-zip": "^0.4.14", "aes256": "^1.0.4", "async": "^2.6.1", "axios": "^1.6.2", "base32-encode": "^1.1.1", "body-parser": "^1.18.3", "cache-base": "^4.0.0", "compression": "^1.7.3", "connect-redis": "^7.1.0", "cookie-parser": "^1.4.4", "cookie-session": "^2.0.0-beta.3", "crypto": "^1.0.1", "crypto-js": "^4.0.0", "dotenv-extended": "^2.3.0", "ejs": "^3.1.9", "eslint": "^5.9.0", "etl": "^0.7.0", "execa": "^4.0.0", "express": "^4.16.4", "express-fileupload": "^1.0.0", "express-session": "^1.17.0", "fast-xml-parser": "^4.3.2", "fs": "0.0.1-security", "he": "^1.2.0", "http-errors": "^1.7.1", "https": "^1.0.0", "ini": "^1.3.8", "lowdb": "^1.0.0", "md5": "^2.2.1", "moment": "^2.29.1", "mysql": "^2.16.0", "node-fetch": "^2.6.1", "nodejs-base64": "^1.0.3", "path": "^0.12.7", "request": "^2.88.0", "soap": "^1.0.0", "url": "^0.11.0", "uuid": "^3.4.0", "xml2json": "^0.12.0"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.7.0", "nodemon": "^2.0.2"}, "scripts": {"build": "babel *.js services/* services/**/* -d dist && node --max-old-space-size=5120 dist/app.js", "start": "nodemon --exec npm run build"}, "nodemonConfig": {"ignore": ["node_modules/*", "dist/*"], "watch": ["services/*", "*.js"], "delay": 2}}