"use strict"
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/
import moment from "moment"
import axios from "axios";
import md5 from "md5";
import CryptoJS from 'crypto-js';

const fs = require('fs');
const xml2jsonParser = require('xml2json');
const ini = require('ini');

class MBClusterManagerBase {
    
    constructor (dbConnector) {        
        this.db = dbConnector;
    }

}
export default MBClusterManagerBase;

