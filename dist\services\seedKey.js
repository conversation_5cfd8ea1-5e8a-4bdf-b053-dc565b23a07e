"use strict";
/*************************
    Seed<PERSON><PERSON> Analyzer Class
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _cryptoJs = require('crypto-js');

var _cryptoJs2 = _interopRequireDefault(_cryptoJs);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var fs = require('fs');
var path = require('path');
var Axios = require('axios');
var execa = require('execa');
var crypto = require('crypto');

var SeedKey = function () {
    function SeedKey(dbConnector, taskManager) {
        _classCallCheck(this, SeedKey);

        this.db = dbConnector;
        this.taskManager = taskManager;

        this.seedkeylibs = process.env.SEEDKEY_LIBS;
        this.seedkeyExe = path.join(__dirname, process.env.SEEDKEY_EXE);
        this.seedkeyIC204Exe = process.env.SEEDKEYIC204_EXE;
        this.seedkeyIC172Py = process.env.SEEDKEYIC172_PY;
        this.seedkeyEZS213Java = process.env.SEEDKEYEZS213_JAVA;
        this.seedkeyEZS167Java = process.env.SEEDKEYEZS167_JAVA;
        this.seedkeyIC177Java = process.env.SEEDKEYIC177_JAVA;
        this.seedkeySPC213Java = process.env.SEEDKEYSPC213_JAVA;
        this.seedkeyEIS447Java = process.env.SEEDKEYEIS447_JAVA;

        this.analyze = this.analyze.bind(this);
        this.list = this.list.bind(this);
        this.calcseedKey = this.calcseedKey.bind(this);
        this.Aes128 = this.Aes128.bind(this);
        this.XorHexString = this.XorHexString.bind(this);
        this.calcseedKeyLevel4 = this.calcseedKeyLevel4.bind(this);
    }

    _createClass(SeedKey, [{
        key: 'analyze',
        value: function analyze(callback) {
            var _this = this;
            if (!fs.existsSync(this.seedkeyExe)) {
                callback({
                    "code": 404,
                    "payload": {
                        "title": "SeedKey Analyzer",
                        "message": "SeedKey File Not Found!"
                    } });
                return;
            }

            try {
                var directoryPath = this.seedkeylibs;
                //passsing directoryPath and callback function
                fs.readdir(directoryPath, function (err, files) {
                    //handling error
                    if (err) {
                        return console.log('Unable to scan directory: ' + err);
                    }
                    //listing all files using forEach
                    files.forEach(function (file) {
                        var _this2 = this;

                        // Do whatever you want to do with the file
                        console.log(file);

                        _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee() {
                            var _ref2, stdout, inputVariables;

                            return regeneratorRuntime.wrap(function _callee$(_context) {
                                while (1) {
                                    switch (_context.prev = _context.next) {
                                        case 0:
                                            _context.next = 2;
                                            return execa(_this.seedkeyExe, [_this.seedkeylibs + "\\" + file, '0', '0000']);

                                        case 2:
                                            _ref2 = _context.sent;
                                            stdout = _ref2.stdout;
                                            inputVariables = JSON.parse(stdout);

                                            console.log(inputVariables);
                                            _this.seedKeyAdd(file, inputVariables["ecuname"], inputVariables["levels"].join(","), inputVariables["seedlen"].join(","), inputVariables["keylen"].join(","), function (result) {});

                                        case 7:
                                        case 'end':
                                            return _context.stop();
                                    }
                                }
                            }, _callee, _this2);
                        }))();
                    });
                });
            } catch (error) {
                console.log(error);
            }

            callback({
                "code": 200,
                "payload": "Completed"
            });
        }
    }, {
        key: 'list',
        value: function list(callback) {
            var SQLquery = { query: "CALL seedkeyList()", params: [] };
            this.db.QueryExec(SQLquery, function (result) {
                if (result !== null) {
                    callback(result[0]);
                }
            });
            //callback([]);
        }
    }, {
        key: 'calcseedKey',
        value: function calcseedKey(filename, classname, level, seed, callback) {
            var _this3 = this;

            var _this = this;
            _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
                var _ref4, stdout, inputVariables;

                return regeneratorRuntime.wrap(function _callee2$(_context2) {
                    while (1) {
                        switch (_context2.prev = _context2.next) {
                            case 0:
                                _context2.prev = 0;

                                console.log(filename, level, seed);
                                _context2.next = 4;
                                return execa(process.env.JAVA_RUNTIME, ['-cp', process.env.SEEDKEYEZS213_PATH, "Unlock_SeedKey", process.env.SEEDKEYEZS213_PATH + "/libs/" + filename, classname, level, seed]);

                            case 4:
                                _ref4 = _context2.sent;
                                stdout = _ref4.stdout;
                                inputVariables = JSON.parse(stdout);

                                stdout = stdout.replace(/,  ]/g, "]").replace(/, ]/g, "]");
                                console.log(inputVariables);
                                callback(_this.encrypt(JSON.stringify(inputVariables)));
                                _context2.next = 16;
                                break;

                            case 12:
                                _context2.prev = 12;
                                _context2.t0 = _context2['catch'](0);

                                callback({ 'error': _context2.t0.code });
                                console.log(_context2.t0);

                            case 16:
                            case 'end':
                                return _context2.stop();
                        }
                    }
                }, _callee2, _this3, [[0, 12]]);
            }))();
        }
    }, {
        key: 'calcseedKeyIC204',
        value: function calcseedKeyIC204(swid, level, seed, callback) {
            var _this4 = this;

            switch (level) {
                case "2709":
                    level = "1";break;
                case "270D":
                    level = "2";break;
                case "2762":
                    level = "3";break;
                case "2772":
                    level = "71";break; // IC172 2771
                case "277D":
                    level = "7D";break; // IC172 270D
                case "2773":
                    level = "7D";break; // IC172 2703
            }

            var _this = this;
            _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
                var _ref6, stdout, inputVariables, _ref7, _stdout, _inputVariables, _ref8, _stdout2, _inputVariables2, _ref9, _stdout3, _inputVariables3, _ref10, _stdout4, _inputVariables4, _ref11, _stdout5, _inputVariables5, _ref12, _stdout6, _inputVariables6, _ref13, _stdout7, _inputVariables7, _ref14, _stdout8, _inputVariables8, _ref15, _stdout9, _inputVariables9, _ref16, _stdout10, _inputVariables10;

                return regeneratorRuntime.wrap(function _callee3$(_context3) {
                    while (1) {
                        switch (_context3.prev = _context3.next) {
                            case 0:
                                _context3.prev = 0;

                                console.log("Request From Client:", swid, level, seed);

                                if (!(swid == "2139026613")) {
                                    _context3.next = 6;
                                    break;
                                }

                                callback(_this.encrypt(JSON.stringify({ "ver": "SGKT Coded By AstronBnX (Reha Bicer)",
                                    "ecuname": "IC204 IC213 IC222", "result": "Ok",
                                    "seed": [],
                                    "key": [],
                                    "levels": [9, 13],
                                    "seedlen": [8, 8],
                                    "keylen": [8, 8],
                                    "keystr": seed.substr(14, 2) + seed.substr(12, 2) + seed.substr(10, 2) + seed.substr(8, 2) + seed.substr(6, 2) + seed.substr(4, 2) + seed.substr(2, 2) + seed.substr(0, 2)
                                })));

                                _context3.next = 144;
                                break;

                            case 6:
                                if (!(level == "2904")) {
                                    _context3.next = 10;
                                    break;
                                }

                                // For IC213 LEvel4
                                _this.calcseedKeyLevel4(swid, level, seed, callback);
                                _context3.next = 144;
                                break;

                            case 10:
                                if (!(swid == "EZS213")) {
                                    _context3.next = 21;
                                    break;
                                }

                                _context3.next = 13;
                                return execa(process.env.JAVA_RUNTIME, ['-cp', process.env.SEEDKEYEZS213_PATH, _this.seedkeyEZS213Java, level, seed]);

                            case 13:
                                _ref6 = _context3.sent;
                                stdout = _ref6.stdout;

                                stdout = stdout.replace(/,  ]/g, "]").replace(/, ]/g, "]");
                                inputVariables = JSON.parse(stdout);

                                console.log(inputVariables);
                                callback(_this.encrypt(JSON.stringify(inputVariables)));
                                _context3.next = 144;
                                break;

                            case 21:
                                if (!(swid == "EZS167")) {
                                    _context3.next = 32;
                                    break;
                                }

                                _context3.next = 24;
                                return execa(process.env.JAVA_RUNTIME, ['-cp', process.env.SEEDKEYEZS213_PATH, _this.seedkeyEZS167Java, level, seed]);

                            case 24:
                                _ref7 = _context3.sent;
                                _stdout = _ref7.stdout;

                                _stdout = _stdout.replace(/,  ]/g, "]").replace(/, ]/g, "]");
                                _inputVariables = JSON.parse(_stdout);

                                console.log(_inputVariables);
                                callback(_this.encrypt(JSON.stringify(_inputVariables)));
                                _context3.next = 144;
                                break;

                            case 32:
                                if (!(swid == "EIS447")) {
                                    _context3.next = 43;
                                    break;
                                }

                                _context3.next = 35;
                                return execa(process.env.JAVA_RUNTIME, ['-cp', process.env.SEEDKEYEZS213_PATH, _this.seedkeyEIS447Java, level, seed]);

                            case 35:
                                _ref8 = _context3.sent;
                                _stdout2 = _ref8.stdout;

                                _stdout2 = _stdout2.replace(/,  ]/g, "]").replace(/, ]/g, "]");
                                _inputVariables2 = JSON.parse(_stdout2);

                                console.log(_inputVariables2);
                                callback(_this.encrypt(JSON.stringify(_inputVariables2)));
                                _context3.next = 144;
                                break;

                            case 43:
                                if (!(swid == "IC177")) {
                                    _context3.next = 54;
                                    break;
                                }

                                _context3.next = 46;
                                return execa(process.env.JAVA_RUNTIME, ['-cp', process.env.SEEDKEYEZS213_PATH, _this.seedkeyIC177Java, level, seed]);

                            case 46:
                                _ref9 = _context3.sent;
                                _stdout3 = _ref9.stdout;

                                _stdout3 = _stdout3.replace(/,  ]/g, "]").replace(/, ]/g, "]");
                                _inputVariables3 = JSON.parse(_stdout3);

                                console.log(_inputVariables3);
                                callback(_this.encrypt(JSON.stringify(_inputVariables3)));
                                _context3.next = 144;
                                break;

                            case 54:
                                if (!(swid == "SPC213")) {
                                    _context3.next = 65;
                                    break;
                                }

                                _context3.next = 57;
                                return execa(process.env.JAVA_RUNTIME, ['-cp', process.env.SEEDKEYEZS213_PATH, _this.seedkeySPC213Java, level, seed]);

                            case 57:
                                _ref10 = _context3.sent;
                                _stdout4 = _ref10.stdout;

                                _stdout4 = _stdout4.replace(/,  ]/g, "]").replace(/, ]/g, "]");
                                _inputVariables4 = JSON.parse(_stdout4);

                                console.log(_inputVariables4);
                                callback(_this.encrypt(JSON.stringify(_inputVariables4)));
                                _context3.next = 144;
                                break;

                            case 65:
                                if (!(level == "71" || level == "7D")) {
                                    _context3.next = 77;
                                    break;
                                }

                                _context3.next = 68;
                                return execa("python3", [_this.seedkeyIC172Py, level, seed]);

                            case 68:
                                _ref11 = _context3.sent;
                                _stdout5 = _ref11.stdout;

                                _stdout5 = _stdout5.replace(/,  ]/g, "]").replace(/, ]/g, "]");
                                _inputVariables5 = JSON.parse(_stdout5);

                                _inputVariables5["keystr"] = _inputVariables5["keystr"].replace(/(.{2})/g, "$1 ").trim();
                                console.log(_inputVariables5);
                                callback(_this.encrypt(JSON.stringify(_inputVariables5)));
                                _context3.next = 144;
                                break;

                            case 77:
                                if (!(level == "2701" || level == "27FD" || level == "2705")) {
                                    _context3.next = 98;
                                    break;
                                }

                                _context3.t0 = level;
                                _context3.next = _context3.t0 === "2701" ? 81 : _context3.t0 === "2705" ? 83 : _context3.t0 === "27FD" ? 85 : 87;
                                break;

                            case 81:
                                level = "1";return _context3.abrupt('break', 87);

                            case 83:
                                level = "5";return _context3.abrupt('break', 87);

                            case 85:
                                level = "FD";return _context3.abrupt('break', 87);

                            case 87:
                                _context3.next = 89;
                                return execa(_this.seedkeyIC204Exe, [swid, level, seed, "221"]);

                            case 89:
                                _ref12 = _context3.sent;
                                _stdout6 = _ref12.stdout;

                                _stdout6 = _stdout6.replace(/,  ]/g, "]").replace(/, ]/g, "]");
                                _inputVariables6 = JSON.parse(_stdout6);

                                _inputVariables6["keystr"] = _inputVariables6["keystr"].replace(/(.{2})/g, "$1 ").trim();
                                console.log(_inputVariables6);
                                callback(_this.encrypt(JSON.stringify(_inputVariables6)));
                                _context3.next = 144;
                                break;

                            case 98:
                                if (!(level == "31FB10")) {
                                    _context3.next = 110;
                                    break;
                                }

                                _context3.next = 101;
                                return execa(_this.seedkeyIC204Exe, [swid, seed.substring(16, 18), seed.substring(0, 16), "211"]);

                            case 101:
                                _ref13 = _context3.sent;
                                _stdout7 = _ref13.stdout;

                                _stdout7 = _stdout7.replace(/,  ]/g, "]").replace(/, ]/g, "]");
                                _inputVariables7 = JSON.parse(_stdout7);

                                _inputVariables7["keystr"] = _inputVariables7["keystr"].replace(/(.{2})/g, "$1 ").trim();
                                console.log(_inputVariables7);
                                callback(_this.encrypt(JSON.stringify(_inputVariables7)));
                                _context3.next = 144;
                                break;

                            case 110:
                                if (!(level == "31FB01")) {
                                    _context3.next = 122;
                                    break;
                                }

                                _context3.next = 113;
                                return execa(_this.seedkeyIC204Exe, [swid, seed.substring(16, 18), seed.substring(0, 16), "164"]);

                            case 113:
                                _ref14 = _context3.sent;
                                _stdout8 = _ref14.stdout;

                                _stdout8 = _stdout8.replace(/,  ]/g, "]").replace(/, ]/g, "]");
                                _inputVariables8 = JSON.parse(_stdout8);

                                _inputVariables8["keystr"] = _inputVariables8["keystr"].replace(/(.{2})/g, "$1 ").trim();
                                console.log(_inputVariables8);
                                callback(_this.encrypt(JSON.stringify(_inputVariables8)));
                                _context3.next = 144;
                                break;

                            case 122:
                                if (!(level == "31FB02")) {
                                    _context3.next = 135;
                                    break;
                                }

                                //for 203
                                console.log(swid, seed, "203");
                                _context3.next = 126;
                                return execa(_this.seedkeyIC204Exe, [swid, seed.substring(8, 12), seed.substring(0, 8), "203"]);

                            case 126:
                                _ref15 = _context3.sent;
                                _stdout9 = _ref15.stdout;

                                _stdout9 = _stdout9.replace(/,  ]/g, "]").replace(/, ]/g, "]");
                                _inputVariables9 = JSON.parse(_stdout9);

                                _inputVariables9["keystr"] = _inputVariables9["keystr"].replace(/(.{2})/g, "$1 ").trim();
                                console.log(_inputVariables9);
                                callback(_this.encrypt(JSON.stringify(_inputVariables9)));
                                _context3.next = 144;
                                break;

                            case 135:
                                _context3.next = 137;
                                return execa(_this.seedkeyIC204Exe, [swid, level, seed]);

                            case 137:
                                _ref16 = _context3.sent;
                                _stdout10 = _ref16.stdout;

                                _stdout10 = _stdout10.replace(/,  ]/g, "]").replace(/, ]/g, "]");
                                _inputVariables10 = JSON.parse(_stdout10);

                                _inputVariables10["keystr"] = _inputVariables10["keystr"].replace(/(.{2})/g, "$1 ").trim();
                                console.log(_inputVariables10);
                                callback(_this.encrypt(JSON.stringify(_inputVariables10)));

                            case 144:
                                _context3.next = 150;
                                break;

                            case 146:
                                _context3.prev = 146;
                                _context3.t1 = _context3['catch'](0);

                                callback({ 'error': _context3.t1.code });
                                console.log(_context3.t1);

                            case 150:
                            case 'end':
                                return _context3.stop();
                        }
                    }
                }, _callee3, _this4, [[0, 146]]);
            }))();
        }
    }, {
        key: 'calcseedKeyLevel4',
        value: function calcseedKeyLevel4(swid, level, input, callback) {
            /*
            31 01 F0 29 01
            71 01 F0 29 01 - static aes key
            71 01 F0 29 02 - eeprom aes key .
            71 01 F0 29 07 - eeprom writeable .
            */

            var _this = this;
            var xor_mask = "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF";

            //var input = "33 FF CF C1 76 73 80 A1 02 00 20 00 06 00 3F 00 23 03 17 07 83 80 91";
            var seed = input.split("|")[1];
            var key = "E5 1A 7F F0 66 81 6F 04 8C F3 5E 4A DA EE 65 1A";

            var JumpValue = 2;

            //if (input.split("|")[0].replace(/\s/g,'').substring(JumpValue*2,JumpValue*2+6).toUpperCase()!="FFFFFF" && input.split("|")[2]=="02") {
            if (input.split("|")[2] == "02") {
                key = input.split("|")[0].substring(JumpValue * 2, JumpValue * 2 + 16 * 2); //Jump 2 byte            
            }
            console.log("Key: ", key);
            try {
                var keyBuffer = Buffer.from(key.replace(/\s/g, '').substring(0, 16 * 2).toString(16), 'hex');
                var firstpart = seed.replace(/\s/g, '').substring(0, 16 * 2);
                var secondpart = seed.replace(/\s/g, '').substring(16 * 2, seed.length) + "FFFFFFFFFFFFFFFF01";

                var result1 = this.Aes128(this.XorHexString(firstpart, xor_mask), keyBuffer);
                var result2 = this.Aes128(this.XorHexString(result1, secondpart), keyBuffer);
                var result = (result1 + result2).toUpperCase().match(/.{1,2}/g).join(' ');
                //console.log(result);
                var inputVariables = {
                    "ecuname": "IC213",
                    "levels": [4],
                    "seed": seed,
                    "keystr": result,
                    "result": "Ok"
                };
                console.log("Level4 Response: ", inputVariables);
                callback(_this.encrypt(JSON.stringify(inputVariables)));
            } catch (error) {
                callback({ 'level4 error': error.code });
                console.log(error);
            }
        }
    }, {
        key: 'seedKeyAdd',
        value: function seedKeyAdd(vFileName, vEcuName, vEcuLevels, vSeedLen, vKeyLen, callback) {
            var SQLquery = { query: "CALL SeedKeyAdd(?,?,?,?,?)", params: [vFileName, vEcuName, vEcuLevels, vSeedLen, vKeyLen] };
            this.db.QueryExec(SQLquery, function (result) {
                if (result !== null && result.affectedRows == 1) {
                    callback(result);
                }
            });
        }
    }, {
        key: 'Aes128',
        value: function Aes128(buffer, key) {
            var _buffer = Buffer.from(buffer, 'hex');
            var cipher = crypto.createCipheriv("aes-128-ecb", key, '');
            cipher.setAutoPadding(false);
            var result = cipher.update(_buffer).toString('hex');
            result += cipher.final().toString('hex');
            return result;
        }
    }, {
        key: 'XorHexString',
        value: function XorHexString(key, mask) {
            var result = '';
            for (var index = 0; index < key.length; index++) {
                var temp = (parseInt(key.charAt(index), 16) ^ parseInt(mask.charAt(index), 16)).toString(16).toUpperCase();
                result += temp;
            }
            return result;
        }
    }, {
        key: 'encrypt',
        value: function encrypt(plaintext) {
            var key = _cryptoJs2.default.enc.Utf8.parse('Pw7dxZs8glfor9tnc2VnsdgH1etkfjcv');
            var iv = _cryptoJs2.default.enc.Utf8.parse('bF1lc3sjd8zpt9cx');
            var message = _cryptoJs2.default.enc.Utf8.parse(plaintext);
            var encrypted = _cryptoJs2.default.AES.encrypt(message, key, {
                keySize: 256,
                iv: iv,
                mode: _cryptoJs2.default.mode.CBC,
                padding: _cryptoJs2.default.pad.Pkcs7
            });
            return encrypted.toString();
        }
    }]);

    return SeedKey;
}();

exports.default = SeedKey;