"use strict";
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _moment = require("moment");

var _moment2 = _interopRequireDefault(_moment);

var _axios = require("axios");

var _axios2 = _interopRequireDefault(_axios);

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

var _mbClusterManagerBase = require("../mbClusterManagerBase");

var _mbClusterManagerBase2 = _interopRequireDefault(_mbClusterManagerBase);

var _utils = require("../../utils");

var _utils2 = _interopRequireDefault(_utils);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return call && (typeof call === "object" || typeof call === "function") ? call : self; }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function, not " + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }

var IC172 = function (_MBClusterManagerBase) {
    _inherits(IC172, _MBClusterManagerBase);

    function IC172(dbConnector) {
        _classCallCheck(this, IC172);

        return _possibleConstructorReturn(this, (IC172.__proto__ || Object.getPrototypeOf(IC172)).call(this, dbConnector));
    }

    _createClass(IC172, [{
        key: "AMGEnable",
        value: function AMGEnable() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1D + "," + 0x99 + "," + 0x02 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGEnableESP",
        value: function AMGEnableESP() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1D + "," + 0x99 + "," + 0x02 + "," + 0xED + "," + 0xEF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGEnable1",
        value: function AMGEnable1() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1D + "," + 0x99 + "," + 0x02 + "," + 0x6F + "," + 0xEF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGEnable2",
        value: function AMGEnable2() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1D + "," + 0x99 + "," + 0x02 + "," + 0x7C + "," + 0xEF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGEnable3",
        value: function AMGEnable3() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1D + "," + 0x99 + "," + 0x02 + "," + 0xEC + "," + 0xEF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGEnable4",
        value: function AMGEnable4() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1D + "," + 0x99 + "," + 0x02 + "," + 0xFE + "," + 0xEF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGEnable5",
        value: function AMGEnable5() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1D + "," + 0x99 + "," + 0x02 + "," + 0x7F + "," + 0xEF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGEnable6",
        value: function AMGEnable6() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1D + "," + 0x99 + "," + 0x02 + "," + 0x7D + "," + 0xEF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGEnableManual",
        value: function AMGEnableManual() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1D + "," + 0x99 + "," + 0x02 + "," + 0x6D + "," + 0xCF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "EnableBiTurbo",
        value: function EnableBiTurbo() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x11 + "," + 0xB8 + "," + 0x01 + "," + 0x01 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "DisableBiTurbo",
        value: function DisableBiTurbo() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x11 + "," + 0xB8 + "," + 0x01 + "," + 0x08 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "EnableBrabus",
        value: function EnableBrabus() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1C + "," + 0xE7 + "," + 0x01 + "," + 0x01 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "DisableBrabus",
        value: function DisableBrabus() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1C + "," + 0xE7 + "," + 0x01 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGDisable",
        value: function AMGDisable() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1D + "," + 0x99 + "," + 0x02 + "," + 0x0E + "," + 0x6E + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "Virgin",
        value: function Virgin() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x23 + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1D + "," + 0x71 + "," + 0x01 + "], \"reply\": [" + 0x63 + "," + 0x24 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1D + "," + 0x71 + "," + 0x01 + "," + 0xA4 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x08 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x23 + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1D + "," + 0x71 + "," + 0x01 + "], \"reply\": [" + 0x63 + "," + 0xA4 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x1D + "," + 0x71 + "," + 0x01 + "," + 0x24 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "EpromEdit",
        value: function EpromEdit() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "Unlock",
        value: function Unlock() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level3\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"level71\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }" + "        ]" + "}";
            return CMDResponse;
        }
    }]);

    return IC172;
}(_mbClusterManagerBase2.default);

exports.default = IC172;