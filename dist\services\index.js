"use strict";
/*************************
    MB Services Classes
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _taskmanager = require("./taskmanager");

var _taskmanager2 = _interopRequireDefault(_taskmanager);

var _seedKey = require("./seedKey");

var _seedKey2 = _interopRequireDefault(_seedKey);

var _mbServer = require("./mbServer");

var _mbServer2 = _interopRequireDefault(_mbServer);

var _mbClusterManager = require("./mbClusterManager");

var _mbClusterManager2 = _interopRequireDefault(_mbClusterManager);

var _mbEISManager = require("./mbEISManager");

var _mbEISManager2 = _interopRequireDefault(_mbEISManager);

var _mbMapGenerator = require("./mbMapGenerator");

var _mbMapGenerator2 = _interopRequireDefault(_mbMapGenerator);

var _paypal = require("./paypal");

var _paypal2 = _interopRequireDefault(_paypal);

var _ecuFlashing = require("./ecuFlashing");

var _ecuFlashing2 = _interopRequireDefault(_ecuFlashing);

var _mbVariantBackup = require("./mbVariantBackup");

var _mbVariantBackup2 = _interopRequireDefault(_mbVariantBackup);

var _mbAntiTheftCode = require("./mbAntiTheftCode");

var _mbAntiTheftCode2 = _interopRequireDefault(_mbAntiTheftCode);

var _mbVINDecoder = require("./mbVINDecoder");

var _mbVINDecoder2 = _interopRequireDefault(_mbVINDecoder);

var _mbHeadUnit = require("./mbHeadUnit");

var _mbHeadUnit2 = _interopRequireDefault(_mbHeadUnit);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var MBServices = function () {
    function MBServices(dbConnector) {
        _classCallCheck(this, MBServices);

        this.db = dbConnector;
        this.taskManager = (0, _taskmanager2.default)();

        this.seedkey = new _seedKey2.default(this.db, this.taskManager);
        this.mbServer = new _mbServer2.default(this.db, this.taskManager);
        this.mbClusterManager = new _mbClusterManager2.default(this.db);
        this.mbEISManager = new _mbEISManager2.default(this.db);
        this.mbMapGenerator = new _mbMapGenerator2.default(this.db);
        this.paypal = new _paypal2.default(this.db);
        this.ecuFlashing = new _ecuFlashing2.default(this.db);
        this.mbVariantBackup = new _mbVariantBackup2.default(this.db);
        this.mbAntiTheftCode = new _mbAntiTheftCode2.default(this.db);
        this.mbVINDecoder = new _mbVINDecoder2.default(this.db);
        this.mbHeadUnit = new _mbHeadUnit2.default(this.db);

        //this.setGarbageCollector();
    }

    _createClass(MBServices, [{
        key: "setGarbageCollector",
        value: function setGarbageCollector() {
            var taskItem = new this.taskManager.TaskItem("Service Garbage Collector");
            taskItem.setTimeOut(15 * 1000);
            taskItem.setWorker(function () {
                try {
                    // GC a Job
                    console.log("GC done...");
                    taskItem.setRunning(false);
                } catch (e) {
                    taskItem.setRunning(false);
                    self.error({
                        "taskid": taskItem.taskId,
                        "error": e
                    });
                }
            });

            this.taskManager.createTask(taskItem);
        }
    }, {
        key: "getMBServiceStates",
        value: function getMBServiceStates() {
            return this.taskManager.taskList();
        }
    }]);

    return MBServices;
}();

exports.default = MBServices;