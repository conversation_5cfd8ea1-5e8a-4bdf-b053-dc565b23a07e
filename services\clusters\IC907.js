"use strict"
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/
import moment from "moment"
import axios from "axios";
import md5 from "md5";
import MBClusterManagerBase from "../mbClusterManagerBase";
import utils from "../../utils";

class IC907 extends MBClusterManagerBase {
    constructor (dbConnector) {     
        super(dbConnector);   

    }

    Virginize2() {        
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 1000 },
                { proc: "init"},
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid"},
                { proc: "securityinit"},
                { proc: "sgkt",  param: ["level61"], reply: [] },                // get software version of ic so we know what magic number to use
                { proc: "sleep", param: 2000 },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x04, 0x04, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x08, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x10, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x20, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x30, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x40, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x19, 0xD4, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x19, 0xE0, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x19, 0xF0, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x1A, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x1A, 0x10, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x1A, 0x20, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x1A, 0x30, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x1A, 0x40, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x1A, 0x50, 0x10, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds",   param: [0x2E, 0x03, 0x21, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x6E, 0x03] },
                { proc: "uds",   param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x10, 0x08, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "init"}
            ]
        }       
        return JSON.stringify(CMDResponse);
    }   


    Virginize() {        
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "uds", param: [0x10, 0x01], reply: [0x50] },
                { proc: "init" },
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid" },
                { proc: "init" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x08, 0x00, 0x20], reply: [0x63] },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x00, 0x75, 0x20], reply: [0x63] },
                { proc: "push" },
                // { proc: "uds", param: [0x31, 0x01, 0xF0, 0x29, 0x01], reply: [0x71, 0x01, 0xF0, 0x29] },
                // { proc: "push" },
                // { proc: "sgkt", param: ["level4"], reply: [] },
                { proc: "uds", param: [0x31, 0x01, 0x08, 0x01], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x01, 0x08, 0x02], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x01, 0x08, 0x06], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x01, 0x03, 0x00, 0x00, 0x00, 0x00], reply: [0x71, 0x01] },
                // { proc: "uds", param: [0x31, 0x01, 0x03, 0x1C], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x04, 0x04, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x08, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x10, 0x20, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x30, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x40, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x24, 0xD4, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x24, 0xE0, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x24, 0xF0, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x25, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x07], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                // { proc: "uds", param: [0x31, 0x01, 0x03, 0x07], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x01, 0x03, 0x05], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x03, 0x05], reply: [0x71, 0x02] }
            ]
        };       
        return JSON.stringify(CMDResponse);
    }    

    EpromEdit() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "uds", param: [0x10, 0x01], reply: [0x50] },
                { proc: "init" },
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid" },
                { proc: "init" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x08, 0x00, 0x20], reply: [0x63] },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x00, 0x75, 0x20], reply: [0x63] },
                { proc: "push" }
                // { proc: "uds", param: [0x31, 0x01, 0xF0, 0x29, 0x01], reply: [0x71, 0x01, 0xF0, 0x29] },
                // { proc: "push" },
                // { proc: "sgkt", param: ["level4"], reply: [] }
            ]
        };        
        return JSON.stringify(CMDResponse);
    }

    Unlock() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "uds", param: [0x10, 0x01], reply: [0x50] },
                { proc: "init" },
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid" },
                { proc: "init" },
                { proc: "securityinit"},
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x08, 0x00, 0x20], reply: [0x63] },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x00, 0x75, 0x20], reply: [0x63] },
                { proc: "push" }
                // { proc: "uds", param: [0x31, 0x01, 0xF0, 0x29, 0x01], reply: [0x71, 0x01, 0xF0, 0x29] },
                // { proc: "push" },
                // { proc: "sgkt", param: ["level4"], reply: [] }
            ]
        };        
        return JSON.stringify(CMDResponse);
    }

    DisableEZSSync() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "uds", param: [0x10, 0x01], reply: [0x50] },
                { proc: "init" },
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid" },
                { proc: "init" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x08, 0x00, 0x20], reply: [0x63] },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x00, 0x75, 0x20], reply: [0x63] },
                { proc: "push" },
                { proc: "uds", param: [0x31, 0x01, 0xF0, 0x29, 0x01], reply: [0x71, 0x01, 0xF0, 0x29] },
                { proc: "push" },
                // { proc: "sgkt", param: ["level4"], reply: [] },
                // { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x08, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x10, 0x08, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x18, 0x01, 0xAA], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" }
            ]
        };        
        return JSON.stringify(CMDResponse);
    }

}
export default IC907;
