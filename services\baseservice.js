"use strict"
/*************************
    Bank Abstract Class
    Coded by <PERSON><PERSON>
**************************/
import moment from "moment"
import axios from "axios";
import { decodeAPI } from '../utils';

class BaseService {
    constructor(dbConnector) {
        this.db = dbConnector;

        if (this.constructor === BaseService) {
            throw new TypeError("Cannot construct abstract class.");
        }

        if (this.accountActivities === BaseService.prototype.accountActivities) {
            throw new TypeError("Please implement abstract method accountActivities.");
        }        
    }    

    updateTask(taskItem,state) {
        let SQLquery = { query: "CALL tsk_UpdateAJob(?,?,?,?)" , params: [taskItem.payload.id,state,taskItem.taskId,taskItem.payload.lastamount] };
        this.db.QueryExec(SQLquery, (result)=>{                      
            if(result === null) {
                console.log("Task Update Error:",taskItem.taskId);
            } else {
                //console.log("Task Update:",result);
                if (result.affectedRows==1) {
                    console.log("Task done:",taskItem.taskId);
                }
            }
        });
    }

    updateTransactions(taskItem,data,accountHistory) {
        console.log("Task Activity Writing DB:",taskItem.taskId);
        taskItem.setProcess("Bank transaction list writing to DB");
        let SQLquery = { query: "CALL tsk_PutTransactions(?,?,?,?,?)" , params: [ 
            taskItem.payload.companyid, 
            taskItem.payload.bankid, 
            taskItem.payload.id, 
            taskItem.payload.bankbranchid, 
            JSON.stringify(data) 
        ]};
        this.db.QueryExec(SQLquery, (result)=>{       
            //console.log("Task Update:",result);               
            if(result === null) {
                taskItem.setProcess("Bank transaction list writing error");
                console.log("Task Activity Error:",taskItem.taskId);
            }    
            accountHistory(taskItem);
        });
    }

    accountActivities(taskItem, getActivities) {

        taskItem.setWorker(() => {
            try {
                // Take a Job
                console.log("Task Get Running:",taskItem.taskId);
                taskItem.setProcess("Getting a job");

                let SQLquery = { query: "CALL tsk_GetAJob(?,?)" , params: [this.bankid,taskItem.taskId] };
                this.db.QueryExec(SQLquery, (result)=>{                      
                    try {
                        if(result === null) {
                            console.log("Task Get Error:",taskItem.taskId);
                            taskItem.setProcess("Job error");
                            taskItem.setRunning(false);
                        } else {
                            if(result) {    
                                let payload = result[0][0];
                                const accountPayload = JSON.parse(payload.accpayload);
                                payload.accpayload = accountPayload;

                                const bankApi = JSON.parse(decodeAPI(payload.companyid,payload.bankid,payload.api));
                                payload.api = bankApi;                            

                                taskItem.setPayload(payload);                        
                                getActivities(taskItem);
                            } else {
                                taskItem.setProcess("No job");
                                console.log("Task done:",taskItem.taskId);
                                taskItem.setRunning(false);
                            }
                        }    
                    } catch(e) {
                        taskItem.setRunning(false);
                        console.log({
                            "taskid": taskItem.taskId,
                            "error": e
                        });
                    }
                });
            } catch(e) {
                taskItem.setRunning(false);
                console.log({
                    "taskid": taskItem.taskId,
                    "error": e
                });
            }
        });

    }

}
export default BaseService;
