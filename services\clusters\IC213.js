"use strict"
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/
import moment from "moment"
import axios from "axios";
import md5 from "md5";
import MBClusterManagerBase from "../mbClusterManagerBase";
import utils from "../../utils";

class IC213 extends MBClusterManagerBase {
    constructor (dbConnector) {     
        super(dbConnector);   

    }

    AMGAnalog() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "uds", param: [0x10, 0x01], reply: [0x50] },
                { proc: "init" },
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid" },
                { proc: "init" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x08, 0x00, 0x20], reply: [0x63] },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x00, 0x75, 0x20], reply: [0x63] },
                { proc: "push" },
                { proc: "uds", param: [0x31, 0x01, 0xF0, 0x29, 0x01], reply: [0x71, 0x01, 0xF0, 0x29] },
                { proc: "push" },
                { proc: "sgkt", param: ["level4"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0E, 0x93, 0x02, 0xF8, 0x00], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x03, 0x05], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x03, 0x05], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMG280KM() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "uds", param: [0x10, 0x01], reply: [0x50] },
                { proc: "init" },
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid" },
                { proc: "init" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x08, 0x00, 0x20], reply: [0x63] },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x00, 0x75, 0x20], reply: [0x63] },
                { proc: "push" },
                { proc: "uds", param: [0x31, 0x01, 0xF0, 0x29, 0x01], reply: [0x71, 0x01, 0xF0, 0x29] },
                { proc: "push" },
                { proc: "sgkt", param: ["level4"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0C, 0xF0, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x00, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x10, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x20, 0x10, 0x00, 0x00, 0x00, 0xF0, 0x7F, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x30, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x00, 0x55, 0x00, 0x55, 0x00, 0xEC, 0x45, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0xD9, 0x01, 0x8B], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0E, 0x93, 0x02, 0xF0, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x00, 0x09, 0x00, 0x00, 0x00, 0x80, 0x20, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x03, 0x05], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x03, 0x05], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMG330KM() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "uds", param: [0x10, 0x01], reply: [0x50] },
                { proc: "init" },
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid" },
                { proc: "init" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x08, 0x00, 0x20], reply: [0x63] },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x00, 0x75, 0x20], reply: [0x63] },
                { proc: "push" },
                { proc: "uds", param: [0x31, 0x01, 0xF0, 0x29, 0x01], reply: [0x71, 0x01, 0xF0, 0x29] },
                { proc: "push" },
                { proc: "sgkt", param: ["level4"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0C, 0xF0, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x00, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x10, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x20, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x30, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0xD9, 0x01, 0x6D], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0E, 0x93, 0x02, 0xE2, 0x00], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x03, 0x05], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x03, 0x05], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    /*

3D 14 02 00 0C F0 10 FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF
3D 14 02 00 0D 00 10 FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF
3D 14 02 00 0D 10 10 FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF
3D 14 02 00 0D 20 10 FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF
3D 14 02 00 0D 30 10 FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF
    
    */

    AMGYellow() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "uds", param: [0x10, 0x01], reply: [0x50] },
                { proc: "init" },
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid" },
                { proc: "init" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x08, 0x00, 0x20], reply: [0x63] },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x00, 0x75, 0x20], reply: [0x63] },
                { proc: "push" },
                { proc: "uds", param: [0x31, 0x01, 0xF0, 0x29, 0x01], reply: [0x71, 0x01, 0xF0, 0x29] },
                { proc: "push" },
                { proc: "sgkt", param: ["level4"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0C, 0xF0, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x00, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x10, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x20, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x30, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0xD9, 0x01, 0x6D], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0E, 0x93, 0x02, 0xE2, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x00, 0x09, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x03, 0x05], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x03, 0x05], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGNoYellow() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "uds", param: [0x10, 0x01], reply: [0x50] },
                { proc: "init" },
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid" },
                { proc: "init" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x08, 0x00, 0x20], reply: [0x63] },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x00, 0x75, 0x20], reply: [0x63] },
                { proc: "push" },
                { proc: "uds", param: [0x31, 0x01, 0xF0, 0x29, 0x01], reply: [0x71, 0x01, 0xF0, 0x29] },
                { proc: "push" },
                { proc: "sgkt", param: ["level4"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x00, 0x09, 0x00, 0x00, 0x00, 0x80, 0x20, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x03, 0x05], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x03, 0x05], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGDisable() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "uds", param: [0x10, 0x01], reply: [0x50] },
                { proc: "init" },
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid" },
                { proc: "init" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x08, 0x00, 0x20], reply: [0x63] },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x00, 0x75, 0x20], reply: [0x63] },
                { proc: "push" },
                { proc: "uds", param: [0x31, 0x01, 0xF0, 0x29, 0x01], reply: [0x71, 0x01, 0xF0, 0x29] },
                { proc: "push" },
                { proc: "sgkt", param: ["level4"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0C, 0xF0, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x00, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x10, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x20, 0x10, 0x00, 0x00, 0x00, 0xF0, 0x7F, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0x30, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x00, 0x55, 0x00, 0x55, 0x00, 0xEC, 0x45, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0D, 0xD9, 0x01, 0x8B], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x0E, 0x93, 0x02, 0xF1, 0x00], reply: [0x7D, 0x14] }, // 0xF9 + 0xF0
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x03, 0x05], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x03, 0x05], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    Virginize() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "uds", param: [0x10, 0x01], reply: [0x50] },
                { proc: "init" },
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid" },
                { proc: "init" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x08, 0x00, 0x20], reply: [0x63] },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x00, 0x75, 0x20], reply: [0x63] },
                { proc: "push" },
                { proc: "uds", param: [0x31, 0x01, 0xF0, 0x29, 0x01], reply: [0x71, 0x01, 0xF0, 0x29] },
                { proc: "push" },
                { proc: "sgkt", param: ["level4"], reply: [] },
                { proc: "uds", param: [0x31, 0x01, 0x08, 0x01], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x01, 0x08, 0x02], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x01, 0x08, 0x06], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x01, 0x03, 0x00, 0x00, 0x00, 0x00], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x01, 0x03, 0x1C], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x04, 0x04, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x08, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x10, 0x20, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x30, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x40, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x24, 0xD4, 0x0C, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x24, 0xE0, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x24, 0xF0, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x25, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x07], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x03, 0x07], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x01, 0x03, 0x05], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x03, 0x05], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    EpromEdit() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "uds", param: [0x10, 0x01], reply: [0x50] },
                { proc: "init" },
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid" },
                { proc: "init" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x08, 0x00, 0x20], reply: [0x63] },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x00, 0x75, 0x20], reply: [0x63] },
                { proc: "push" },
                { proc: "uds", param: [0x31, 0x01, 0xF0, 0x29, 0x01], reply: [0x71, 0x01, 0xF0, 0x29] },
                { proc: "push" },
                { proc: "sgkt", param: ["level4"], reply: [] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    Unlock() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "uds", param: [0x10, 0x01], reply: [0x50] },
                { proc: "init" },
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid" },
                { proc: "init" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x08, 0x00, 0x20], reply: [0x63] },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x00, 0x75, 0x20], reply: [0x63] },
                { proc: "push" },
                { proc: "uds", param: [0x31, 0x01, 0xF0, 0x29, 0x01], reply: [0x71, 0x01, 0xF0, 0x29] },
                { proc: "push" },
                { proc: "sgkt", param: ["level4"], reply: [] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    DisableEZSSync() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "uds", param: [0x10, 0x01], reply: [0x50] },
                { proc: "init" },
                { proc: "uds", param: [0x22, 0xF1, 0x11], reply: [0x62] },
                { proc: "getswid" },
                { proc: "init" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["level61"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x08, 0x00, 0x20], reply: [0x63] },
                { proc: "uds", param: [0x23, 0x14, 0x02, 0x00, 0x00, 0x75, 0x20], reply: [0x63] },
                { proc: "push" },
                { proc: "uds", param: [0x31, 0x01, 0xF0, 0x29, 0x01], reply: [0x71, 0x01, 0xF0, 0x29] },
                { proc: "push" },
                { proc: "sgkt", param: ["level4"], reply: [] },
                { proc: "sleep", param: 2000 },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x08, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x10, 0x08, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x02, 0x00, 0x00, 0x18, 0x01, 0xAA], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

}
export default IC213;

