"use strict"
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/
import moment from "moment"
import axios from "axios";
import md5 from "md5";
import MBClusterManagerBase from "../mbClusterManagerBase";
import utils from "../../utils";

class IC204 extends MBClusterManagerBase {
    constructor (dbConnector) {     
        super(dbConnector);   

    }

    TachoDizel() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x04, 0x50, 0x10, 0x00, 0x00, 0x22, 0x01, 0x2C, 0x01, 0xD0, 0x07, 0xA0, 0x0F, 0x70, 0x17, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    TachoC63() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x04, 0x50, 0x10, 0x00, 0x00, 0x22, 0x01, 0x2C, 0x01, 0xD0, 0x07, 0x88, 0x13, 0x7E, 0x1D, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    TachoBenzin() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x04, 0x50, 0x10, 0x00, 0x00, 0x22, 0x01, 0x2C, 0x01, 0xD0, 0x07, 0x88, 0x13, 0x40, 0x1F, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    Speedo320KM() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                // { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x04, 0x50, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0xE8, 0x03, 0x40, 0x1F, 0x80, 0x3E, 0xC0, 0x5D, 0x00, 0x7D], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x04, 0x28, 0x08, 0x40, 0x1F, 0x80, 0x3E, 0xC0, 0x5D, 0x00, 0x7D], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    Speedo260KM() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x04, 0x50, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0xE8, 0x03, 0x40, 0x1F, 0xE0, 0x2E, 0x50, 0x46, 0x90, 0x65], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x04, 0x28, 0x08, 0x40, 0x1F, 0xE0, 0x2E, 0x50, 0x46, 0x90, 0x65], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    Speedo160ML() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x04, 0x50, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0xE8, 0x03, 0xA0, 0x0F, 0x58, 0x1B, 0xF8, 0x2A, 0x80, 0x3E], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x04, 0x28, 0x08, 0xA0, 0x0F, 0x58, 0x1B, 0xF8, 0x2A, 0x80, 0x3E], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    Speedo340Brabus() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x04, 0x50, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0xE8, 0x03, 0xA0, 0x1F, 0xE0, 0x2E, 0x50, 0x46, 0xD0, 0x84], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGEnable() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x02, 0x69, 0x01, 0x12], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x01, 0xD0, 0x01, 0x60], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGEnable5PotDiesel() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x02, 0x69, 0x01, 0xE0], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x01, 0xD0, 0x01, 0x60], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGW212W218Enable() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x02, 0x69, 0x01, 0x60], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x01, 0xD0, 0x01, 0x60], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGW218Enable() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x02, 0x69, 0x01, 0x10], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x01, 0xD0, 0x01, 0x60], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGWMonoEnable() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x80, 0x00, 0x01, 0xD0, 0x02, 0x8C, 0x1C], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGDisable() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x02, 0x69, 0x01, 0xFA], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x01, 0xD0, 0x01, 0xFF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    // 2189021001 = 3d 14 8f 00 0a 0C 04 00 00 00 00
    // 2129026108 = 3d 14 8f 00 0a 24 04 00 00 00 00  SSID 3D 14 8f 00 0a 44 04 fe ff ff ff 
    // 2129024109 = 3D 14 8F 00 0A 0C 04 00 00 00 00  SSID 3D 14 8F 00 0A 2C 04 FE FF FF FF  Level8 = 3D 14 10 00 02 C4 02 6A 48  Level2 = 3D 14 10 00 02 C4 02 C4 A2
    // 2129029710 = 3D 14 8F 00 0A 0C 04 00 00 00 00  SSID 3D 14 8F 00 0A 2C 04 FE FF FF FF  Level8 = 3D 14 10 00 02 C4 02 6A 48  Level2 = 3D 14 10 00 02 C4 02 C4 A2
    // 2129029806 = 3D 14 8F 00 0A 28 04 00 00 00 00  SSID 3D 14 8F 00 0A 48 04 fe ff ff ff 

    Virgin(swid) {
        let unlockSteps = [];

        switch (swid) {
            case "2189021001":
                unlockSteps = [
                    { proc: "uds", param: [0x3D, 0x14, 0x8F, 0x00, 0x0A, 0x0C, 0x04, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] }
                ];
                break;
            case "2129026108":
                unlockSteps = [
                    { proc: "uds", param: [0x3D, 0x14, 0x8F, 0x00, 0x0A, 0x24, 0x04, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] }
                ];
                break;
            case "2129024109":
                unlockSteps = [
                    { proc: "uds", param: [0x3D, 0x14, 0x8F, 0x00, 0x0A, 0x0C, 0x04, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] }
                ];
                break;
            case "2129029710":
                unlockSteps = [
                    { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x02, 0xC4, 0x02, 0x6A, 0x48], reply: [0x7D, 0x14] },
                    { proc: "hardreset" },
                    { proc: "sleep", param: 3000 },
                    { proc: "init" },
                    { proc: "getswid" },
                    { proc: "sgkt", param: ["level9"], reply: [] },
                    { proc: "securityinit" },
                    { proc: "sgkt", param: ["leveld"], reply: [] },
                    { proc: "sleep", param: 2000 },
                    { proc: "uds", param: [0x31, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00], reply: [0x71, 0x01] },
                    { proc: "uds", param: [0x3D, 0x14, 0x8F, 0x00, 0x0A, 0x0C, 0x04, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                    { proc: "uds", param: [0x3D, 0x14, 0x8F, 0x00, 0x0A, 0x2C, 0x04, 0xFE, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                    { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x02, 0xC4, 0x02, 0xC4, 0xA2], reply: [0x7D, 0x14] }
                ];
                break;
            case "2129029806":
                unlockSteps = [
                    { proc: "uds", param: [0x3D, 0x14, 0x8F, 0x00, 0x0A, 0x28, 0x04, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] }
                ];
                break;
        }

        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "sleep", param: 2000 },
                ...unlockSteps,
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x00, 0x00, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x00, 0x10, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x00, 0x20, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x00, 0x30, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x00, 0x40, 0x10, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x10, 0x00, 0x00, 0x50, 0x10, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    VirginMono() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x80, 0x00, 0x00, 0x00, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x80, 0x00, 0x00, 0x10, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x80, 0x00, 0x00, 0x20, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x80, 0x00, 0x00, 0x30, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x80, 0x00, 0x00, 0x40, 0x10, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x14] },
                { proc: "uds", param: [0x3D, 0x14, 0x80, 0x00, 0x00, 0x50, 0x10, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    EpromEdit() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    Unlock() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level9"], reply: [] },
                { proc: "securityinit" },
                { proc: "sgkt", param: ["leveld"], reply: [] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

}
export default IC204;

