"use strict";
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _moment = require("moment");

var _moment2 = _interopRequireDefault(_moment);

var _axios = require("axios");

var _axios2 = _interopRequireDefault(_axios);

var _MBAdaptor = require("./MBAdaptor");

var _MBAdaptor2 = _interopRequireDefault(_MBAdaptor);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var fs = require('fs');
var xml2jsonParser = require('xml2json');
var ini = require('ini');

var MBServer = function () {
    function MBServer(dbConnector, taskManager) {
        _classCallCheck(this, MBServer);

        this.db = dbConnector;
        this.taskManager = taskManager;
        this.mbAdaptor = new _MBAdaptor2.default(this.db, this.taskManager);

        this.getscn = this.getscn.bind(this);
        this.getecus = this.getecus.bind(this);
        this.getflashwares = this.getflashwares.bind(this);

        this.loadTemplate = this.loadTemplate.bind(this);
        this.findElement = this.findElement.bind(this);
        this.createAEDHeader = this.createAEDHeader.bind(this);
    }

    _createClass(MBServer, [{
        key: "createAEDHeader",
        value: function createAEDHeader(ecuname, hwid, segmentCount) {
            var header = "[Description]\n" + "Title=DTS Monaco additional ECU data\n" + "ProductVersion=8.14.016\n" + "Schema=7.01\n" + "Creator=Softing\n" + "Date=30/08/2020\n" + "Time=19:11:25\n" + "Author=\n" + "Comment=\n" + "NoOfConfigurations=1\n" + "[Configuration_001]\n" + "NoOfDomains=" + segmentCount + "\n" + "Title=" + ecuname + "\n" + "Comment=HWID:" + hwid + "\n";
            return header;
        }
    }, {
        key: "getecus",
        value: function getecus(vin, callback) {
            var _this = this;
            if (typeof vin === "undefined" || vin.length != 17) {
                var error = {
                    msg: "Invalid VIN number"
                };
                console.log(error);
                callback({ 'error': error });
                return;
            }

            var config = {
                useVedoc: true,
                hwid: "",
                process: "",
                diagName: ""
            };

            try {
                this.mbAdaptor.loginMBServer(vin, config, callback);
            } catch (error) {
                console.log(error);
                callback({ 'error': error });
            }
        }
    }, {
        key: "getflashwares",
        value: function getflashwares(vin, config, callback) {
            var _this = this;
            if (typeof vin === "undefined" || vin.length != 17) {
                var error = {
                    msg: "Invalid VIN number"
                };
                console.log(error);
                callback({ 'error': error });
                return;
            }

            try {
                /*
                axios.post('https://app.remoteobd.net/xentry-api/flashware', this.PostCarData(vin,ecuname))
                .then(function (response) {
                    var flashwares = ini.parse(response.data);
                    console.log(flashwares.ECU1.ECU_Refer);
                    console.log(flashwares.ECU1.SG_DiogName);
                    console.log(flashwares.ECU1.SG_REFNR);
                    console.log(flashwares.ECU1.SG_Current_SW);
                    console.log(flashwares.ECU1.SG_FITTINGFLASHSW);
                    console.log(flashwares.ECU1.SG_NEWFLASHSW);
                      callback({
                        "ecurefer":flashwares.ECU1.ECU_Refer,
                        "ecuname":flashwares.ECU1.SG_DiogName,
                        "hwid":flashwares.ECU1.SG_REFNR,
                        "currentsw":flashwares.ECU1.SG_Current_SW,
                        "fittingsw":flashwares.ECU1.SG_FITTINGFLASHSW,
                        "newsw":flashwares.ECU1.SG_NEWFLASHSW,
                        "sacode":flashwares.ETN.SACode
                    });
                })
                .catch(function (error) {
                    console.log(error);
                    callback({'error':error.code});
                });
                */
                try {
                    this.mbAdaptor.loginMBServer(vin, config, function (data) {
                        var flashwares = ini.parse(data);
                        callback({
                            "ecurefer": flashwares.ECU1.ECU_Refer,
                            "ecuname": flashwares.ECU1.SG_DiogName,
                            "hwid": flashwares.ECU1.SG_REFNR,
                            "currentsw": flashwares.ECU1.SG_Current_SW,
                            "fittingsw": flashwares.ECU1.SG_FITTINGFLASHSW,
                            "newsw": flashwares.ECU1.SG_NEWFLASHSW,
                            "sacode": flashwares.ETN.SACode
                        });
                    });
                } catch (error) {
                    console.log(error);
                    callback({ 'error': error });
                }
            } catch (error) {
                console.log(error);
                callback({ 'error': error });
            }
        }
    }, {
        key: "getscn",
        value: function getscn(vin, config, callback) {
            var _this = this;
            if (typeof vin === "undefined" || vin.length != 17) {
                var error = {
                    msg: "Invalid VIN number"
                };
                console.log(error);
                callback({ 'error': error });
                return;
            }

            try {
                this.mbAdaptor.loginMBServer(vin, config, function (data) {

                    var scncount = data["ecu"]["new"]["segmentsCount"];
                    var scndata = data["ecu"]["new"]["segments"];
                    var partnumbers = data["ecu"]["new"]["parts"]["part"];
                    var hwid = data["ecu"]["hwPartNumber"];

                    var CodingElements = _this.loadTemplate(config.diagName + ".vcx");
                    var AEDBody = _this.createAEDHeader(config.diagName, hwid, scncount);

                    for (var i = 0; i < scncount; i++) {
                        var element = _this.findElement(CodingElements, scndata["segment"][i]["segmentNumber"]);
                        if (element != null) {
                            AEDBody += (element["CodingServiceName"].substring(0, 4) == "VCD_" ? "" : "VCD_") + element["CodingServiceName"].replace("_Read_Dump", "") + "=" + String(scndata["segment"][i]["codestring"]).match(/.{1,2}/g).join(',') + "\n";
                        }
                    }
                    callback({ "aed": AEDBody, "parts": partnumbers });
                });
            } catch (error) {
                console.log(error);
                callback({ 'error': error });
            }

            /*
                    try {
                        axios.post('https://app.remoteobd.net/xentry-api/scn', this.PostCarData(vin,ecuname))
                        .then(function (response) {
                          let data = response.data["ns3:ascframe"].payload[0]["ns2:scnResponse"][0]["ecus"][0]["ecu"][0];          
                
                          var scncount = data["new"][0]["segmentsCount"];
                          var scndata = data["new"][0]["segments"][0];
                          var partnumbers = data["new"][0]["parts"][0]["part"];
                          var hwid = data["hwPartNumber"];
                
                          var CodingElements = _this.loadTemplate(ecuname+".vcx");                             
                          var AEDBody = _this.createAEDHeader(ecuname,hwid,scncount);
                                
                          for(var i=0;i<scncount;i++) {      
                              var element = _this.findElement(CodingElements,scndata["segment"][i]["segmentNumber"]);
                              if (element!=null) {
                                  AEDBody += "VCD_"+element["CodingServiceName"].replace("_Read_Dump","") + "=" + String(scndata["segment"][i]["codestring"]).match(/.{1,2}/g).join(',')+"\n";
                              }              
                          }
                          callback({"aed":AEDBody,"parts":partnumbers});
                        })
                        .catch(function (error) {
                            callback({'error':error.response.data.error});
                        });      
                    } catch(error) {
                        console.log(error);            
                        callback({'error':error});
                    }
            */
        }
    }, {
        key: "loadTemplate",
        value: function loadTemplate(templateName) {

            var data = fs.readFileSync(process.env.SCN_TEMP + templateName);
            var json = JSON.parse(xml2jsonParser.toJson(data));

            var CodingDataInformation = json["VehicleActivityDataExchange"]["FlashCodingElement"]["CodingElements"]["CodingDataInformation"];
            return CodingDataInformation;
        }
    }, {
        key: "findElement",
        value: function findElement(CodingDataInformation, SegmentNumber) {
            for (var i = 0; i < CodingDataInformation.length; i++) {
                if (parseInt(CodingDataInformation[i]["SegmentNumber"]) == parseInt(SegmentNumber)) {
                    return CodingDataInformation[i];
                }
            }
            return null;
        }
    }]);

    return MBServer;
}();

exports.default = MBServer;