"use strict"
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/
import moment from "moment"
import axios from "axios";
import md5 from "md5";
import MBClusterManagerBase from "../mbClusterManagerBase";
import utils from "../../utils";

class KI203 extends MBClusterManagerBase {
    constructor (dbConnector) {     
        super(dbConnector);   

    }

    Virgin() {        
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 3000 },
                { proc: "uds",   param: [0x10, 0x92], reply: [0x50, 0x92] },
                { proc: "uds",   param: [0x10, 0xF0], reply: [0x50, 0xF0] },
                { proc: "sgkt",  param: ["levelver"], reply: [] },               // get software version of ic so we know what magic number to use
                { proc: "sgkt",  param: ["level10"], reply: [] },                 // do seedkey, but nobody knows why it is called "level10"
                { proc: "uds",   param: [0x21, 0x11], reply: [0x61, 0x11, 0x07] },
                { proc: "uds",   param: [ 0x3D, 0x08, 0x00, 0x00, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF ], reply: [ 0x7D, 0x08 ] },
                { proc: "uds",   param: [ 0x3D, 0x08, 0x00, 0x10, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF ], reply: [ 0x7D, 0x08 ] },
                { proc: "uds",   param: [ 0x3D, 0x08, 0x03, 0x00, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF ], reply: [ 0x7D, 0x08 ] },
                { proc: "uds",   param: [ 0x3D, 0x08, 0x03, 0x10, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF ], reply: [ 0x7D, 0x08 ] },
                { proc: "uds",   param: [ 0x3D, 0x08, 0x00, 0x36, 0x04, 0xFE, 0xFF, 0xFF, 0xFF], reply: [ 0x7D, 0x08 ] },
                { proc: "uds",   param: [ 0x3D, 0x08, 0x00, 0x20, 0x04, 0x00, 0x00, 0x00, 0x00], reply: [ 0x7D, 0x08 ] },
                { proc: "hardreset" },
                { proc: "sleep", param: 3000 },
            ]
        }       
        return JSON.stringify(CMDResponse);
    }   

    EpromEdit() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 3000 },
                { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] },
                { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] },
                { proc: "sgkt", param: ["levelver"], reply: [] },               // get software version of ic so we know what magic number to use
                { proc: "sgkt", param: ["level10"], reply: [] },                 // do seedkey, but nobody knows why it is called "level10"
                { proc: "uds", param: [0x21, 0x11], reply: [0x61, 0x11, 0x07] }
            ]
        }       
        return JSON.stringify(CMDResponse);
    }

    Unlock() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 3000 },
                { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] },
                { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] },
                { proc: "sgkt", param: ["levelver"], reply: [] },               // get software version of ic so we know what magic number to use
                { proc: "sgkt", param: ["level10"], reply: [] },                // do seedkey, but nobody knows why it is called "level10"
                { proc: "uds", param: [0x21, 0x11], reply: [0x61, 0x11, 0x07] }
            ]
        }       
        return JSON.stringify(CMDResponse);
    }

}

export default KI203;

