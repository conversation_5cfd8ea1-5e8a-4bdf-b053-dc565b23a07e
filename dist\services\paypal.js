"use strict";
/*************************
    PayPal Class
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _moment = require("moment");

var _moment2 = _interopRequireDefault(_moment);

var _axios = require("axios");

var _axios2 = _interopRequireDefault(_axios);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _asyncToGenerator(fn) { return function () { var gen = fn.apply(this, arguments); return new Promise(function (resolve, reject) { function step(key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { return Promise.resolve(value).then(function (value) { step("next", value); }, function (err) { step("throw", err); }); } } return step("next"); }); }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var paypal = require('@paypal/checkout-server-sdk');

// German Paypal Account                                                                            // Test Account
var CLIENT = 'ARSnw6d1sW1me8UTAqFSQenbPY0lggeAdvsP3Xchn4ktgrXbKAfoIf01gpassdJk6nQOvvvOCOXLay-3'; //'AXm2ksOkrOaEz-jR1Ws_cnJU0UPS7w6uhlTQoZPHCxx6feADu9LuVzcJ7rmDqLdZS46l0MqmhCWkJyCL';
var SECRET = 'EKfiX7ZvLBN5XvngcJSJ_ujtZBWpIdZh9xYlPEpUQdCNGM9il1jm1l5R8IF0QCErz7S0cx1Yi6OYggH5'; //'EDEpRm-gfgEoxCqkwRa1agS92OMRvuSK8_GHmL9T0AWrDdE4XW_gxooelWSsZ1CBk4NZ54z9CJ0FQrXw';

// Czech Paypal Account
//const CLIENT = 'AeHMTRDX5wnoZdkgHHIrPsI2OQMmhLIbv01tc6bhN0o4TBmZAIn7i4NvIfboHMhIButxlxJEaQVOPtJn'; //AQP24GxBBuTcSTA6s5WbK2OBokAeJmIF1gkbjS3MBI2N9vf7wLKhafMrvucu0ISnV40bg2FdUMiFx-wo
//const SECRET = 'EPz76AHRZwFP_qWE-X-0wmI3zqSNA24YeA5XlRgNDrOD1zKnzJU4CSevCuQPxn0CKrmQRnx1HpT4ae-r'; //EEXu-ezAYaXw1OSmYg7FN6A8rllZLcWSHKTpKT3poAfv4aGE8k6Qtf_tjQQ6qM_hlHachmmQPLJDvxaf

var Paypal = function () {
    function Paypal(dbConnector) {
        _classCallCheck(this, Paypal);

        this.db = dbConnector;

        this.client = this.client.bind(this);
        this.environment = this.environment.bind(this);
        this.createPayment = this.createPayment.bind(this);
        this.executePayment = this.executePayment.bind(this);
    }

    _createClass(Paypal, [{
        key: "environment",
        value: function environment() {
            var clientId = CLIENT || 'PAYPAL-SANDBOX-CLIENT-ID';
            var clientSecret = SECRET || 'PAYPAL-SANDBOX-CLIENT-SECRET';

            if (process.env.NODE_ENV === 'production') {
                return new paypal.core.LiveEnvironment(clientId, clientSecret);
            }
            return new paypal.core.SandboxEnvironment(clientId, clientSecret);
        }
    }, {
        key: "client",
        value: function client() {
            return new paypal.core.PayPalHttpClient(this.environment());
        }
    }, {
        key: "createPayment",
        value: function createPayment(sku, callback) {
            var _this2 = this;

            //this.connectPaypal('CAPTURE', '1.23', 'EUR', 'Buying IC213 AMG Cluster Menu', callback);
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenCalc(?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [sku] };
            _this.db.QueryExec(SQLquery, function () {
                var _ref = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee(result) {
                    var request, order;
                    return regeneratorRuntime.wrap(function _callee$(_context) {
                        while (1) {
                            switch (_context.prev = _context.next) {
                                case 0:
                                    request = new paypal.orders.OrdersCreateRequest();

                                    request.prefer("return=representation");
                                    request.requestBody({
                                        intent: 'AUTHORIZE',
                                        purchase_units: [{
                                            amount: {
                                                currency_code: 'EUR',
                                                value: result[1][0].Price
                                            },
                                            description: result[1][0].Caption
                                        }]
                                    });

                                    order = void 0;
                                    _context.prev = 4;
                                    _context.next = 7;
                                    return _this2.client().execute(request);

                                case 7:
                                    order = _context.sent;
                                    _context.next = 14;
                                    break;

                                case 10:
                                    _context.prev = 10;
                                    _context.t0 = _context["catch"](4);

                                    // 4. Handle any errors from the call
                                    console.error(_context.t0);
                                    callback(404, { "error": _context.t0 });

                                case 14:

                                    // 5. Return a successful response to the client with the order ID
                                    console.log("Connect Paypal:", order);
                                    callback(200, { "orderid": order.result.id });

                                case 16:
                                case "end":
                                    return _context.stop();
                            }
                        }
                    }, _callee, _this2, [[4, 10]]);
                }));

                return function (_x) {
                    return _ref.apply(this, arguments);
                };
            }());
        } // end of function

    }, {
        key: "executePayment",
        value: function () {
            var _ref2 = _asyncToGenerator( /*#__PURE__*/regeneratorRuntime.mark(function _callee2(userid, sku, orderID, authorizationid, callback) {
                var request, order, _this, SQLquery;

                return regeneratorRuntime.wrap(function _callee2$(_context2) {
                    while (1) {
                        switch (_context2.prev = _context2.next) {
                            case 0:
                                //this.connectPaypal('AUTHORIZE', '1.23', 'EUR', 'Buying IC213 AMG Cluster Menu', callback);
                                request = new paypal.orders.OrdersGetRequest(orderID);
                                order = void 0;
                                _context2.prev = 2;
                                _context2.next = 5;
                                return this.client().execute(request);

                            case 5:
                                order = _context2.sent;
                                _context2.next = 12;
                                break;

                            case 8:
                                _context2.prev = 8;
                                _context2.t0 = _context2["catch"](2);


                                // 4. Handle any errors from the call
                                console.error(_context2.t0);
                                callback(404, { "error": _context2.t0.message });

                            case 12:

                                // 5. Validate the transaction details are as expected
                                console.log("Connect Paypal:", order);

                                // `tokenBuy`(IN vUserId int, IN vOrderId varchar(50), IN vSku varchar(20), OUT vReturn INT)
                                _this = this;
                                SQLquery = { query: "Set @ReturnValue=0; CALL tokenBuy(?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, orderID, authorizationid, sku] };

                                _this.db.QueryExec(SQLquery, function (result) {
                                    callback(200, { "orderid": order.result.id });
                                });

                            case 16:
                            case "end":
                                return _context2.stop();
                        }
                    }
                }, _callee2, this, [[2, 8]]);
            }));

            function executePayment(_x2, _x3, _x4, _x5, _x6) {
                return _ref2.apply(this, arguments);
            }

            return executePayment;
        }()
    }]);

    return Paypal;
}();

exports.default = Paypal;