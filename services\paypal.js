"use strict"
/*************************
    PayPal Class
    Coded by <PERSON><PERSON>
**************************/
import moment from "moment"
import axios from "axios";
const paypal = require('@paypal/checkout-server-sdk');

// German Paypal Account                                                                            // Test Account
const CLIENT = 'ARSnw6d1sW1me8UTAqFSQenbPY0lggeAdvsP3Xchn4ktgrXbKAfoIf01gpassdJk6nQOvvvOCOXLay-3'; //'AXm2ksOkrOaEz-jR1Ws_cnJU0UPS7w6uhlTQoZPHCxx6feADu9LuVzcJ7rmDqLdZS46l0MqmhCWkJyCL';
const SECRET = 'EKfiX7ZvLBN5XvngcJSJ_ujtZBWpIdZh9xYlPEpUQdCNGM9il1jm1l5R8IF0QCErz7S0cx1Yi6OYggH5'; //'EDEpRm-gfgEoxCqkwRa1agS92OMRvuSK8_GHmL9T0AWrDdE4XW_gxooelWSsZ1CBk4NZ54z9CJ0FQrXw';

// Czech Paypal Account
//const CLIENT = 'AeHMTRDX5wnoZdkgHHIrPsI2OQMmhLIbv01tc6bhN0o4TBmZAIn7i4NvIfboHMhIButxlxJEaQVOPtJn'; //AQP24GxBBuTcSTA6s5WbK2OBokAeJmIF1gkbjS3MBI2N9vf7wLKhafMrvucu0ISnV40bg2FdUMiFx-wo
//const SECRET = 'EPz76AHRZwFP_qWE-X-0wmI3zqSNA24YeA5XlRgNDrOD1zKnzJU4CSevCuQPxn0CKrmQRnx1HpT4ae-r'; //EEXu-ezAYaXw1OSmYg7FN6A8rllZLcWSHKTpKT3poAfv4aGE8k6Qtf_tjQQ6qM_hlHachmmQPLJDvxaf

class Paypal {
    constructor (dbConnector) {        
        this.db = dbConnector;

        this.client = this.client.bind(this); 
        this.environment = this.environment.bind(this); 
        this.createPayment = this.createPayment.bind(this); 
        this.executePayment = this.executePayment.bind(this);         
    }

    environment() {
        let clientId = CLIENT || 'PAYPAL-SANDBOX-CLIENT-ID';
        let clientSecret = SECRET || 'PAYPAL-SANDBOX-CLIENT-SECRET';
    
        if (process.env.NODE_ENV === 'production') {
            return new paypal.core.LiveEnvironment(clientId, clientSecret);
        }
            return new paypal.core.SandboxEnvironment(clientId, clientSecret);
    }

    client() {
        return new paypal.core.PayPalHttpClient(this.environment());
    }

    createPayment(sku,callback) {
        //this.connectPaypal('CAPTURE', '1.23', 'EUR', 'Buying IC213 AMG Cluster Menu', callback);
        let _this = this;        
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenCalc(?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ sku ] };        
        _this.db.QueryExec(SQLquery, async (result)=>{  

            const request = new paypal.orders.OrdersCreateRequest();
            request.prefer("return=representation");
            request.requestBody({
            intent: 'AUTHORIZE',            
            purchase_units: [{
                amount: {
                currency_code: 'EUR',
                value: result[1][0].Price
                },
                description: result[1][0].Caption
            }]
            });
        
            let order;
            try {
            order = await this.client().execute(request);
            } catch (err) {      
            // 4. Handle any errors from the call
            console.error(err);
            callback(404,{"error":err});
            }
        
            // 5. Return a successful response to the client with the order ID
            console.log("Connect Paypal:",order);
            callback(200,{"orderid": order.result.id});
        });

    } // end of function

    async executePayment(userid, sku, orderID, authorizationid, callback) {        
        //this.connectPaypal('AUTHORIZE', '1.23', 'EUR', 'Buying IC213 AMG Cluster Menu', callback);
        let request = new paypal.orders.OrdersGetRequest(orderID);

        let order;
        try {
          order = await this.client().execute(request);
        } catch (err) {
      
          // 4. Handle any errors from the call
          console.error(err);
          callback(404,{"error":err.message});
        }
      
        // 5. Validate the transaction details are as expected
        console.log("Connect Paypal:",order);

        // `tokenBuy`(IN vUserId int, IN vOrderId varchar(50), IN vSku varchar(20), OUT vReturn INT)
        let _this = this;        
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenBuy(?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, orderID, authorizationid, sku ] };        
        _this.db.QueryExec(SQLquery, (result)=>{              
            callback(200,{"orderid": order.result.id});      
        });
    }

    
}

export default Paypal;
