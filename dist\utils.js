"use strict";
/*************************
    Utils
    Coded by <PERSON><PERSON>
**************************/

var _cryptoJs = require('crypto-js');

var _cryptoJs2 = _interopRequireDefault(_cryptoJs);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var aes256 = require('aes256');
var md5 = require('md5');

var _require = require('nodejs-base64'),
    base64encode = _require.base64encode,
    base64decode = _require.base64decode;

/**
 * An API to allow for greatly simplified AES-256 encryption and decryption using a passphrase of
 * any length plus a random Initialization Vector.
 * @exports aes256
 * @public
 */
var utils = {

    encodeAPI: function encodeAPI(companyid, bankid, api) {
        return aes256.encrypt(md5('ny' + companyid + '' + bankid), api);
    },

    decodeAPI: function decodeAPI(companyid, bankid, encrypted) {
        return aes256.decrypt(md5('ny' + companyid + '' + bankid), encrypted);
    },

    encrypt: function encrypt(plaintext) {
        var key = _cryptoJs2.default.enc.Utf8.parse('Pw7dxZs8glfor9tnc2VnsdgH1etkfjcv');
        var iv = _cryptoJs2.default.enc.Utf8.parse('bF1lc3sjd8zpt9cx');
        var message = _cryptoJs2.default.enc.Utf8.parse(plaintext);
        var encrypted = _cryptoJs2.default.AES.encrypt(message, key, {
            keySize: 256,
            iv: iv,
            mode: _cryptoJs2.default.mode.CBC,
            padding: _cryptoJs2.default.pad.Pkcs7
        });
        return encrypted.toString();
    },

    decrypt: function decrypt(ciphertext) {
        var key = _cryptoJs2.default.enc.Utf8.parse('Pw7dxZs8glfor9tnc2VnsdgH1etkfjcv');
        var iv = _cryptoJs2.default.enc.Utf8.parse('bF1lc3sjd8zpt9cx');
        var encryptedPassword = EncodingUtil.base64Decode(ciphertext);
        var decryptedPassword = _cryptoJs2.default.AES.decrypt(encryptedPassword, key, iv);
        return decryptedPassword.toString();
    }
};

//
// Export the API
//
module.exports = utils;