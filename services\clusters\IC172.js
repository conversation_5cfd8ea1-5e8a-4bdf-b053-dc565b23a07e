"use strict"
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/
import moment from "moment"
import axios from "axios";
import md5 from "md5";
import MBClusterManagerBase from "../mbClusterManagerBase";
import utils from "../../utils";

class IC172 extends MBClusterManagerBase {
    constructor (dbConnector) {     
        super(dbConnector);   

    }

    AMGEnable() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x1D, 0x99, 0x02, 0xFF, 0xFF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGEnableESP() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x1D, 0x99, 0x02, 0xED, 0xEF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGEnable1() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x1D, 0x99, 0x02, 0x6F, 0xEF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGEnable2() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x1D, 0x99, 0x02, 0x7C, 0xEF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGEnable3() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x1D, 0x99, 0x02, 0xEC, 0xEF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGEnable4() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x1D, 0x99, 0x02, 0xFE, 0xEF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGEnable5() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x1D, 0x99, 0x02, 0x7F, 0xEF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGEnable6() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x1D, 0x99, 0x02, 0x7D, 0xEF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGEnableManual() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x1D, 0x99, 0x02, 0x6D, 0xCF], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    EnableBiTurbo() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x11, 0xB8, 0x01, 0x01], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    DisableBiTurbo() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x11, 0xB8, 0x01, 0x08], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    EnableBrabus() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x1C, 0xE7, 0x01, 0x01], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    DisableBrabus() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x1C, 0xE7, 0x01, 0x00], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    AMGDisable() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x1D, 0x99, 0x02, 0x0E, 0x6E], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x09], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x02, 0x00, 0x09], reply: [0x71, 0x02] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    Virgin() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x23, 0x14, 0x00, 0x00, 0x1D, 0x71, 0x01], reply: [0x63, 0x24] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x1D, 0x71, 0x01, 0xA4], reply: [0x7D, 0x14] },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x02, 0x00, 0x00, 0x00], reply: [0x71, 0x01] },
                { proc: "uds", param: [0x31, 0x01, 0x00, 0x08], reply: [0x71, 0x01] },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] },
                { proc: "uds", param: [0x23, 0x14, 0x00, 0x00, 0x1D, 0x71, 0x01], reply: [0x63, 0xA4] },
                { proc: "uds", param: [0x3D, 0x14, 0x00, 0x00, 0x1D, 0x71, 0x01, 0x24], reply: [0x7D, 0x14] },
                { proc: "sleep", param: 2000 },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    EpromEdit() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

    Unlock() {
        let CMDResponse = {
            workflow: [
                { proc: "clear" },
                { proc: "hardreset" },
                { proc: "sleep", param: 2000 },
                { proc: "init" },
                { proc: "getswid" },
                { proc: "sgkt", param: ["level3"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["level71"], reply: [] },
                { proc: "sleep", param: 1000 },
                { proc: "sgkt", param: ["leveld"], reply: [] }
            ]
        };
        return JSON.stringify(CMDResponse);
    }

}

export default IC172;
