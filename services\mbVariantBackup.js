"use strict"
/*************************
    MB Variant Backup
    Coded by <PERSON><PERSON>
**************************/
import md5 from "md5";

class MBVariantBackup {
    constructor (dbConnector) {
        this.db = dbConnector;
        
        this.createTokenId = this.createTokenId.bind(this); 
        this.backup = this.backup.bind(this); 
        this.variants = this.variants.bind(this);
    }    
    
    createTokenId(hwid,swid) {        
        return md5(hwid+swid);
    }

    variants(ecu,callback) {
        let _this = this;        
        let SQLquery = { query: "CALL ecuVariants(?);" , params: [ ecu ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[0]);
        });    
    }
    
    backup(userid,ecu,cid,serial,hwid,swid,diagname,variants,callback) {
        let _this = this;
        let SQLquery = { query: "Set @ReturnValue=0; CALL ecuVariantBackup(?,?,?,?,?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, ecu, cid, serial, hwid, swid, diagname, variants ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });    
    }

    check(userid,ecu,serial,callback) {
        let _this = this;
        let SQLquery = { query: "Set @ReturnValue=0; CALL ecuVariantsCheck(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, ecu, serial ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });    
    }

    get(userid,ecu,serial,callback) {
        let _this = this;
        let SQLquery = { query: "CALL ecuVariantsGet(?,?,?);" , params: [ userid, ecu, serial ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[0]);
        });    
    }

    error(userid,ecu,codes,callback) {
        let _this = this;
        let SQLquery = { query: "CALL ecuErrorCheck(?,?,?);" , params: [ userid, ecu, codes ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            console.log(result);
            callback(result[0]);
        });    
    }
    
}
export default MBVariantBackup;
