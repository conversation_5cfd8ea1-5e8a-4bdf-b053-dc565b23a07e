"use strict";
/*************************
    Seed<PERSON>ey Analyzer Class
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _cryptoJs = require('crypto-js');

var _cryptoJs2 = _interopRequireDefault(_cryptoJs);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var fs = require('fs');
var path = require('path');
var Axios = require('axios');
var execa = require('execa');
var crypto = require('crypto');

var ECUFlasing = function () {
    function ECUFlasing(dbConnector) {
        _classCallCheck(this, ECUFlasing);

        this.db = dbConnector;
    }

    _createClass(ECUFlasing, [{
        key: 'getMagicNumber',
        value: function getMagicNumber(name, variant, callback) {
            var response = {};

            // 41C64E6D , 84a7f399
            if (name == "IC222") {
                /*
                switch(variant)
                {
                    case "2229041300":
                        response = ["41C64E6D", "16088F1D"]; //IC222_14_28_01
                        break;
                    case "2229048700":
                        response = ["41C64E6D", "AF8A3FE9"]; //IC222_13_10_00
                        break;
                    case "2229049400":
                        response = ["41C64E6D", "12345678"]; //IC222_12_16_00
                        break;
                    case "2229046800":
                        response = ["41C64E6D", "84A7F399"]; //IC222_12_25_00
                        break;
                }  
                */

                switch (variant) {
                    case "32E2":
                        response = ["41C64E6D", "16088F1D"]; //IC222_14_28_01
                        break;
                    case "2CE0":
                    case "2CE1":
                        response = ["41C64E6D", "AF8A3FE9"]; //IC222_13_10_00
                        break;
                    case "ABE1":
                    case "ABE2":
                    case "ABE3":
                    case "ABE6":
                    case "ACE1":
                    case "ACE3":
                    case "ACE4":
                    case "ACE5":
                    case "B2E0":
                    case "B2E1":
                    case "B2E2":
                        response = ["41C64E6D", "12345678"]; //IC222_12_16_00
                        break;
                    case "2BE0":
                    case "2BE3":
                    case "2BE4":
                    case "32E1":
                        response = ["41C64E6D", "84A7F399"]; //IC222_12_25_00
                        break;
                }
            }
            console.log(response);
            callback(this.encrypt(JSON.stringify({ "keystr": response })));
        }
    }, {
        key: 'encrypt',
        value: function encrypt(plaintext) {
            var key = _cryptoJs2.default.enc.Utf8.parse('Pw7dxZs8glfor9tnc2VnsdgH1etkfjcv');
            var iv = _cryptoJs2.default.enc.Utf8.parse('bF1lc3sjd8zpt9cx');
            var message = _cryptoJs2.default.enc.Utf8.parse(plaintext);
            var encrypted = _cryptoJs2.default.AES.encrypt(message, key, {
                keySize: 256,
                iv: iv,
                mode: _cryptoJs2.default.mode.CBC,
                padding: _cryptoJs2.default.pad.Pkcs7
            });
            return encrypted.toString();
        }
    }]);

    return ECUFlasing;
}();

exports.default = ECUFlasing;