"use strict";
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _moment = require("moment");

var _moment2 = _interopRequireDefault(_moment);

var _axios = require("axios");

var _axios2 = _interopRequireDefault(_axios);

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

var _mbClusterManagerBase = require("../mbClusterManagerBase");

var _mbClusterManagerBase2 = _interopRequireDefault(_mbClusterManagerBase);

var _utils = require("../../utils");

var _utils2 = _interopRequireDefault(_utils);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return call && (typeof call === "object" || typeof call === "function") ? call : self; }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function, not " + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }

var IC204 = function (_MBClusterManagerBase) {
    _inherits(IC204, _MBClusterManagerBase);

    function IC204(dbConnector) {
        _classCallCheck(this, IC204);

        return _possibleConstructorReturn(this, (IC204.__proto__ || Object.getPrototypeOf(IC204)).call(this, dbConnector));
    }

    _createClass(IC204, [{
        key: "TachoDizel",
        value: function TachoDizel() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x04 + "," + 0x50 + "," + 0x10 + "," + 0x00 + "," + 0x00 + "," + 0x22 + "," + 0x01 + "," + 0x2C + "," + 0x01 + "," + 0xD0 + "," + 0x07 + "," + 0xA0 + "," + 0x0F + "," + 0x70 + "," + 0x17 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "TachoC63",
        value: function TachoC63() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x04 + "," + 0x50 + "," + 0x10 + "," + 0x00 + "," + 0x00 + "," + 0x22 + "," + 0x01 + "," + 0x2C + "," + 0x01 + "," + 0xD0 + "," + 0x07 + "," + 0x88 + "," + 0x13 + "," + 0x7E + "," + 0x1D + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "TachoBenzin",
        value: function TachoBenzin() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x04 + "," + 0x50 + "," + 0x10 + "," + 0x00 + "," + 0x00 + "," + 0x22 + "," + 0x01 + "," + 0x2C + "," + 0x01 + "," + 0xD0 + "," + 0x07 + "," + 0x88 + "," + 0x13 + "," + 0x40 + "," + 0x1F + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "Speedo320KM",
        value: function Speedo320KM() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," +
            //        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x04 + "," + 0x50 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0xE8 + "," + 0x03 + "," + 0x40 + "," + 0x1F + "," + 0x80 + "," + 0x3E + "," + 0xC0 + "," + 0x5D + "," + 0x00 + "," + 0x7D + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
            "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x04 + "," + 0x28 + "," + 0x08 + "," + 0x40 + "," + 0x1F + "," + 0x80 + "," + 0x3E + "," + 0xC0 + "," + 0x5D + "," + 0x00 + "," + 0x7D + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "Speedo260KM",
        value: function Speedo260KM() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x04 + "," + 0x50 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0xE8 + "," + 0x03 + "," + 0x40 + "," + 0x1F + "," + 0xE0 + "," + 0x2E + "," + 0x50 + "," + 0x46 + "," + 0x90 + "," + 0x65 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x04 + "," + 0x28 + "," + 0x08 + "," + 0x40 + "," + 0x1F + "," + 0xE0 + "," + 0x2E + "," + 0x50 + "," + 0x46 + "," + 0x90 + "," + 0x65 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "Speedo160ML",
        value: function Speedo160ML() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x04 + "," + 0x50 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0xE8 + "," + 0x03 + "," + 0xA0 + "," + 0x0F + "," + 0x58 + "," + 0x1B + "," + 0xF8 + "," + 0x2A + "," + 0x80 + "," + 0x3E + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x04 + "," + 0x28 + "," + 0x08 + "," + 0xA0 + "," + 0x0F + "," + 0x58 + "," + 0x1B + "," + 0xF8 + "," + 0x2A + "," + 0x80 + "," + 0x3E + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "Speedo340Brabus",
        value: function Speedo340Brabus() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x04 + "," + 0x50 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0xE8 + "," + 0x03 + "," + 0xA0 + "," + 0x1F + "," + 0xE0 + "," + 0x2E + "," + 0x50 + "," + 0x46 + "," + 0xD0 + "," + 0x84 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGEnable",
        value: function AMGEnable() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x02 + "," + 0x69 + "," + 0x01 + "," + 0x12 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x01 + "," + 0xD0 + "," + 0x01 + "," + 0x60 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGEnable5PotDiesel",
        value: function AMGEnable5PotDiesel() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x02 + "," + 0x69 + "," + 0x01 + "," + 0xE0 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x01 + "," + 0xD0 + "," + 0x01 + "," + 0x60 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGW212W218Enable",
        value: function AMGW212W218Enable() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x02 + "," + 0x69 + "," + 0x01 + "," + 0x60 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x01 + "," + 0xD0 + "," + 0x01 + "," + 0x60 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGW218Enable",
        value: function AMGW218Enable() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x02 + "," + 0x69 + "," + 0x01 + "," + 0x10 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x01 + "," + 0xD0 + "," + 0x01 + "," + 0x60 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGWMonoEnable",
        value: function AMGWMonoEnable() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x80 + "," + 0x00 + "," + 0x01 + "," + 0xD0 + "," + 0x02 + "," + 0x8C + "," + 0x1C + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGDisable",
        value: function AMGDisable() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x02 + "," + 0x69 + "," + 0x01 + "," + 0xFA + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x01 + "," + 0xD0 + "," + 0x01 + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }

        // 2189021001 = 3d 14 8f 00 0a 0C 04 00 00 00 00
        // 2129026108 = 3d 14 8f 00 0a 24 04 00 00 00 00  SSID 3D 14 8f 00 0a 44 04 fe ff ff ff 
        // 2129024109 = 3D 14 8F 00 0A 0C 04 00 00 00 00  SSID 3D 14 8F 00 0A 2C 04 FE FF FF FF  Level8 = 3D 14 10 00 02 C4 02 6A 48  Level2 = 3D 14 10 00 02 C4 02 C4 A2
        // 2129029710 = 3D 14 8F 00 0A 0C 04 00 00 00 00  SSID 3D 14 8F 00 0A 2C 04 FE FF FF FF  Level8 = 3D 14 10 00 02 C4 02 6A 48  Level2 = 3D 14 10 00 02 C4 02 C4 A2
        // 2129029806 = 3D 14 8F 00 0A 28 04 00 00 00 00  SSID 3D 14 8F 00 0A 48 04 fe ff ff ff 

    }, {
        key: "Virgin",
        value: function Virgin(swid) {
            var UnlockAddress = "";

            switch (swid) {
                case "2189021001":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x8F + "," + 0x00 + "," + 0x0A + "," + 0x0C + "," + 0x04 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] },";
                    break;
                case "2129026108":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x8F + "," + 0x00 + "," + 0x0A + "," + 0x24 + "," + 0x04 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] },";
                    break;
                case "2129024109":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x8F + "," + 0x00 + "," + 0x0A + "," + 0x0C + "," + 0x04 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] },";
                    break;
                case "2129029710":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x02 + "," + 0xC4 + "," + 0x02 + "," + 0x6A + "," + 0x48 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] },";
                    UnlockAddress += "{ \"proc\": \"hardreset\" },";
                    UnlockAddress += "{ \"proc\": \"sleep\", \"param\": 3000},";
                    UnlockAddress += "{ \"proc\": \"init\"},";
                    UnlockAddress += "{ \"proc\": \"getswid\"},";
                    UnlockAddress += "{ \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] },";
                    UnlockAddress += "{ \"proc\": \"securityinit\"},";
                    UnlockAddress += "{ \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] },";
                    UnlockAddress += "{ \"proc\": \"sleep\", \"param\": 2000},";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x8F + "," + 0x00 + "," + 0x0A + "," + 0x0C + "," + 0x04 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x8F + "," + 0x00 + "," + 0x0A + "," + 0x2C + "," + 0x04 + "," + 0xFE + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x02 + "," + 0xC4 + "," + 0x02 + "," + 0xC4 + "," + 0xA2 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] },";
                    break;
                case "2129029806":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x8F + "," + 0x00 + "," + 0x0A + "," + 0x28 + "," + 0x04 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] },";
                    break;
            }

            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + UnlockAddress + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x00 + "," + 0x10 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x00 + "," + 0x20 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x00 + "," + 0x30 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x00 + "," + 0x40 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFE + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x10 + "," + 0x00 + "," + 0x00 + "," + 0x50 + "," + 0x10 + "," + 0x00 + "," + 0x00 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "VirginMono",
        value: function VirginMono() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x80 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x80 + "," + 0x00 + "," + 0x00 + "," + 0x10 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x80 + "," + 0x00 + "," + 0x00 + "," + 0x20 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x80 + "," + 0x00 + "," + 0x00 + "," + 0x30 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x80 + "," + 0x00 + "," + 0x00 + "," + 0x40 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFE + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x80 + "," + 0x00 + "," + 0x00 + "," + 0x50 + "," + 0x10 + "," + 0x00 + "," + 0x00 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "EpromEdit",
        value: function EpromEdit() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "Unlock",
        value: function Unlock() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level9\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"leveld\"], \"reply\": [] }" + "        ]" + "}";
            return CMDResponse;
        }
    }]);

    return IC204;
}(_mbClusterManagerBase2.default);

exports.default = IC204;