"use strict";

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();
/*************************
    MB Services Classes
    Coded by <PERSON><PERSON>er
**************************/


var _console = require("console");

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var crypto = require("crypto");
var base32Encode = require('base32-encode');

var MBMapGenerator = function () {
    function MBMapGenerator(dbConnector) {
        _classCallCheck(this, MBMapGenerator);

        this.db = dbConnector;

        this.get = this.get.bind(this);
        this.add = this.add.bind(this);
        this.tokenCheck = this.tokenCheck.bind(this);
        this.tokenAdd = this.tokenAdd.bind(this);
        this.tokenConfirm = this.tokenConfirm.bind(this);

        this.generateMapCode = this.generateMapCode.bind(this);
    }

    _createClass(MBMapGenerator, [{
        key: "getMaps",
        value: function getMaps(HU, callback) {
            var _this = this;
            var SQLquery = { query: "CALL mapcodetypeGet(?);", params: [HU] };
            _this.db.QueryExec(SQLquery, function (result) {
                if (result !== null) {
                    callback(result[0]);
                } else {
                    callback(null);
                }
            });
        }
    }, {
        key: "tokenCheck",
        value: function tokenCheck(userid, cid, sku, hwid, swid, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenCheck(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, (0, _md2.default)(cid + sku + hwid + swid), sku] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }, {
        key: "tokenAdd",
        value: function tokenAdd(userid, cid, sku, hwid, swid, state, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenAdd(?,?,?,?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, (0, _md2.default)(cid + sku + hwid + swid), cid, sku, hwid, swid, state] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }, {
        key: "tokenConfirm",
        value: function tokenConfirm(userid, cid, sku, hwid, swid, state, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenConfirm(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, (0, _md2.default)(cid + sku + hwid + swid), state] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }, {
        key: "add",
        value: function add(userid, HU, vin, swid, callback) {
            var _this = this;
            var SQLquery = { query: "CALL mapcodeAdd(?,?,?,?);", params: [userid, HU, vin, swid] };
            _this.db.QueryExec(SQLquery, function (result) {
                if (result !== null) {
                    callback(result[0]);
                } else {
                    callback(null);
                }
            });
        }
    }, {
        key: "get",
        value: function get(userid, vin, swid, callback) {
            var _this = this;
            var SQLquery = { query: "CALL mapcodeGet(?,?,?);", params: [userid, vin, swid] };
            _this.db.QueryExec(SQLquery, function (result) {
                if (result !== null) {
                    callback(result[0]);
                } else {
                    callback(null);
                }
            });
        }
    }, {
        key: "generateMapCode",
        value: function generateMapCode(userid, HU, swid, vin, callback) {
            var _this2 = this;

            var resultFromGen = {
                vin: "",
                swid: "",
                mapcode: "",
                hu: "",
                state: 0,
                error: ""
            };

            this.get(userid, vin, swid, function (dbresult) {
                if (dbresult != null && dbresult[0] != null) {
                    // Send Existing Data from DB                
                    resultFromGen.vin = dbresult[0].vin;
                    resultFromGen.swid = dbresult[0].swid;
                    resultFromGen.mapcode = dbresult[0].mapcode;
                    resultFromGen.hu = dbresult[0].hu;
                    resultFromGen.state = dbresult[0].state;
                    callback(resultFromGen);
                } else {
                    // Added to DB for Generation Code
                    _this2.add(userid, HU, vin, swid, function (dbresult) {
                        resultFromGen.vin = dbresult[0].vin;
                        resultFromGen.swid = dbresult[0].swid;
                        resultFromGen.mapcode = dbresult[0].mapcode;
                        resultFromGen.hu = dbresult[0].hu;
                        resultFromGen.state = dbresult[0].state;
                        console.log("add from gen", resultFromGen);
                        callback(resultFromGen);
                    });
                }
            });
        }
    }, {
        key: "createTokenId",
        value: function createTokenId(vin) {
            return (0, _md2.default)(vin);
        }
    }]);

    return MBMapGenerator;
}();

exports.default = MBMapGenerator;