"use strict"
/*************************
    MB Variant Backup
    Coded by <PERSON><PERSON>
**************************/
import md5 from "md5";

class MBVINDecoder {
    constructor (dbConnector) {
        this.db = dbConnector;
        
        this.createTokenId = this.createTokenId.bind(this); 
        this.get = this.get.bind(this);
        this.add = this.add.bind(this);
        this.vinDecode = this.vinDecode.bind(this);
        this.tokenAdd = this.tokenAdd.bind(this);
        this.tokenCheck = this.tokenCheck.bind(this);
        this.tokenConfirm = this.tokenConfirm.bind(this);
    }    
    
    createTokenId(serial) {        
        return serial;
    }

    add(vin,callback) {
        let _this = this;        
        let SQLquery = { query: "CALL sacodeAdd(?);" , params: [ vin ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[0]);
        });    
    }
    
    get(vin,callback) {
        let _this = this;
        let SQLquery = { query: "CALL sacodeGet(?);" , params: [ vin ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[0]);
        });    
    }

    vinDecode(serial,callback) {   
        let resultFromGen = {
            brand: "",
            model: "",
            bodytype: "",
            production: "",
            bodycolor: "",
            interiorcolor: "",
            engineno: "",
            transmissionno: "",
            sacodes: [],
            swids: [],
            found: 0,
            state: 0,
            error: ""
        }
        
        this.get(serial,(dbresult)=>{            
            if (dbresult!=null && dbresult.length!=1) {
                // Added to DB for Generation Code
                this.add(serial, (dbresult)=>{
                    resultFromGen.brand = dbresult[0].brand;
                    resultFromGen.model = dbresult[0].model;
                    resultFromGen.bodytype = dbresult[0].bodytype;
                    resultFromGen.production = dbresult[0].production;
                    resultFromGen.bodycolor = dbresult[0].bodycolor;
                    resultFromGen.interiorcolor = dbresult[0].interiorcolor;
                    resultFromGen.engineno = dbresult[0].engineno;
                    resultFromGen.transmissionno = dbresult[0].transmissionno;
                    resultFromGen.sacodes = dbresult[0].sacodes;
                    resultFromGen.swids = dbresult[0].swids;
                    resultFromGen.found = 0;                    
                    resultFromGen.state = dbresult[0].state;
                    callback(resultFromGen);
                });   
            } else {
                // Send Existing Data from DB
                if (dbresult[0]!=null) {
                    resultFromGen.brand = dbresult[0].brand;
                    resultFromGen.model = dbresult[0].model;
                    resultFromGen.bodytype = dbresult[0].bodytype;
                    resultFromGen.production = dbresult[0].production;
                    resultFromGen.bodycolor = dbresult[0].bodycolor;
                    resultFromGen.interiorcolor = dbresult[0].interiorcolor;
                    resultFromGen.engineno = dbresult[0].engineno;
                    resultFromGen.transmissionno = dbresult[0].transmissionno;
                    resultFromGen.sacodes = dbresult[0].sacodes;
                    resultFromGen.swids = dbresult[0].swids;
                    resultFromGen.found = 1;                    
                    resultFromGen.state = dbresult[0].state;
                }
                callback(resultFromGen);
            }
        });        
    }

    tokenCheck(userid,cid,sku,callback) {
        let _this = this;        
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenCheck(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, cid, sku ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });    
    }
    
    tokenAdd(userid,cid,sku,theftcode,state,callback) {
        let _this = this;
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenAdd(?,?,?,?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, cid, cid, sku, cid, theftcode, state ] };                        
        console.log(SQLquery);
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });    
    }

    tokenConfirm(userid,cid,state,callback) {
        let _this = this;
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenConfirm(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, cid, state ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });   
    }      
    
}
export default MBVINDecoder;
