"use strict";
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var _require = require('uuid'),
    uuidv4 = _require.v4;

var axios = require('axios');
var https = require('https');
var xml2jsonParser = require('xml2json');
var moment = require('moment');

var Cookie = [];
var AccessToken = [];
var AccessCookie = [];
var AccessAuthorization = "";
var UserInformation = "";
var CompanyInformation = "";

var XentryVersion = "2020_09";
var DaiHardwareId = "A61CB6ED3E74";
var DaiApplicationId = "255";
//const MBUsername = "c5arhobo"; 
//const MBPassword = "AronHobo2910";
//const MBUsername = "d5ctorok"; 
//const MBPassword = "Small0000!";
var MBUsername = "D5ybalku";
var MBPassword = "Mercedes2020";
var IBCatalogue = "IBCatalogue_0000000571_20201115_115941.zip";
var CatalogueId = "538";

/*
4BFFE7710DD7 252
4DD173A2D043 252
8AA9286ABB70 254
8E0F85FCA370 255
86A3EB150911 254
A5C886D6CE3A 255
A9D595CABECE 255
A125E69EDDF9 255
A7494BBFE41D 255
AA2F66F677C3 255
AC563CCC1611 255
AFFDC791DFBB 255
*/

var MBAdaptor = function () {
    function MBAdaptor(dbConnector, taskManager) {
        _classCallCheck(this, MBAdaptor);

        this.db = dbConnector;
        this.taskManager = taskManager;

        this.ab2str = this.ab2str.bind(this);
        this.OpenURL = this.OpenURL.bind(this);
        this.loginMBServer = this.loginMBServer.bind(this);
        this.zblConnector = this.zblConnector.bind(this);
        this.scnConnector = this.scnConnector.bind(this);
        this.flashwareConnector = this.flashwareConnector.bind(this);
        this.getVehicleDatacard = this.getVehicleDatacard.bind(this);
        this.getToken = this.getToken.bind(this);
        this._zblConnector = this._zblConnector.bind(this);
        this._scnConnector = this._scnConnector.bind(this);
        this._flashwareConnector = this._flashwareConnector.bind(this);
        this._loginMBServer = this._loginMBServer.bind(this);
    }

    _createClass(MBAdaptor, [{
        key: 'ab2str',
        value: function ab2str(buf) {
            return String.fromCharCode.apply(null, new Uint16Array(buf));
        }
    }, {
        key: 'OpenURL',
        value: function OpenURL(method, host, path, headers, data, resCallback, dataCallback, endCallback, errorCallback) {
            var options = {
                hostname: host, //'xdiag-aftersales.i.daimler.com',
                port: 443,
                path: path, //'/xdiag/uis/loggedOk',
                method: method,
                headers: headers
            };

            var req = https.request(options, function (res) {
                resCallback(res);

                res.on('data', function (d) {
                    dataCallback(res, d);
                });
                res.on('end', function (d) {
                    endCallback(res);
                });
            });

            req.on('error', function (error) {
                errorCallback(res, error);
            });

            req.write(data);
            req.end();
        }
    }, {
        key: 'loginMBServer',
        value: function loginMBServer(vin, config, callback) {
            var _this = this;
            var response = {};

            var login = new Promise(function (resolve, reject) {
                _this._loginMBServer(resolve, reject);
            });
            login.then(function (cevap) {
                _this.zblConnector(vin, config, callback);
            }).catch(function (hata) {
                console.log(hata);
            });
        }
    }, {
        key: 'zblConnector',
        value: function zblConnector(vin, config, callback) {
            var _this = this;
            var response = {};

            var zbl = new Promise(function (resolve, reject) {
                _this._zblConnector(resolve, reject, vin);
            });
            zbl.then(function (CarInformation) {
                console.log("CarInformation is sent");
                response = CarInformation;

                switch (config.process) {
                    // Get SCN Codings        
                    case "scn":
                        response = _this.scnConnector(CarInformation["fin"], "MB_PKW", CarInformation["series"], CarInformation["modelDesignation"], CarInformation["technicalApprovalDate"], CarInformation["equipmentCodes"], CarInformation["ecus"], config, callback);
                        break;

                    // Get Flashwares        
                    case "flashware":
                        response = _this.flashwareConnector(CarInformation["fin"], "MB_PKW", CarInformation["series"], CarInformation["modelDesignation"], CarInformation["ttz"], CarInformation["equipmentCodes"], CarInformation["ecus"], config, callback);
                        break;
                    default:
                        callback(response);
                }
            }).catch(function (hata) {
                console.log(hata);
            });
        }
    }, {
        key: 'scnConnector',
        value: function scnConnector(vin, category, series, modelDesignation, dateOfTechnicalState, sacodes, ecus, config, callback) {
            var _this = this;

            var scn = new Promise(function (resolve, reject) {
                _this._scnConnector(resolve, reject, vin, category, series, modelDesignation, dateOfTechnicalState, sacodes, ecus, config);
            });
            scn.then(function (scn) {
                callback(scn);
                console.log(scn);
            }).catch(function (hata) {
                console.log(hata);
            });
        }
    }, {
        key: 'flashwareConnector',
        value: function flashwareConnector(vin, category, series, modelDesignation, ttz, sacodes, ecus, config, callback) {
            var _this = this;

            var flash = new Promise(function (resolve, reject) {
                _this._flashwareConnector(resolve, reject, vin, category, series, modelDesignation, ttz, sacodes, ecus, config);
            });
            flash.then(function (flashware) {
                callback(flashware);
                //console.log(flashware);
            }).catch(function (hata) {
                console.log(hata);
            });
        }
    }, {
        key: 'getVehicleDatacard',
        value: function getVehicleDatacard(vin) {
            this.OpenURL("GET", "xdiag-aftersales.i.daimler.com", "/xdiag/afs/vehicle_datacard/" + vin + "?fields=baseInfo&fields=codeInfo&fields=majorAssembliesBaseInfo&fields=salesData&fields=controlUnitInfo", {
                'Authorization': AccessToken["token_type"] + " " + AccessToken["access_token"],
                'DaiHardwareId': DaiHardwareId,
                'DaiApplicationId': DaiApplicationId
            }, "", function (res) {}, function (res, data) {
                console.log(this.ab2str(data));
            }, function (res) {}, function (res, error) {});
        }
    }, {
        key: '_zblConnector',
        value: function _zblConnector(resolve, reject, vin) {
            var _this = this;

            var now = new Date();
            var readyData = '<?xml+version="1.0"+encoding="UTF-8"+standalone="yes"?>' + '<ns2:ascframe+xsi:type="ns2:serviceRequestFrame"+frameVersion="1.1"+xsi:schemaLocation="http://asconnector.daimler.com/ascFrame+ascFrame_v11.xsd+http://asconnector.daimler.com/common+ascCommon_v11.xsd"+ xmlns:ns2="http://asconnector.daimler.com/ascFrame"+ xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">' + '    <tracking>' + '        <sessionID>' + uuidv4() + '</sessionID>' + '        <requestID>1605514207197</requestID>' + '    </tracking>' + '    <client>' + '        <platform>Open+Shell</platform>' + '        <dateTime>' + moment().add(-10, 'hours').format("YYYY-MM-DDTHH:mm:ss.SSS") + 'Z</dateTime>' + // 2020-11-16T08:10:06.656Z
            '        <locale>' + '            <language>en</language>' + '            <country>GB</country>' + '        </locale>' + '        <application>' + '            <name>XentryDiagnostics</name>' + '            <version></version>' + '            <patch></patch>' + '        </application>' + '        <task>' + '            <process>CobraConverter</process>' + '            <step></step>' + '            <mode>ONLINE</mode>' + '        </task>' + '    </client>' + '    <destination>' + '        <service>zbl01</service>' + '        <action>getVehicleInfo</action>' + '    </destination>' + '    <user>' + '        <userID>' + MBUsername + '</userID>' + '        <organisationIDs/>' + '    </user>' + '    <payload>' + '        <ns2:getVehicleInfoRequest+routingType="NFZ"+version="1.8.8"+xsi:schemaLocation="http://asconnector.daimler.com/zbl+zblMessages_V1.8.8.xsd"+ xmlns:ns2="http://asconnector.daimler.com/zbl">' + '            <finOrVin>' + vin + '</finOrVin>' + '        </ns2:getVehicleInfoRequest>' + '    </payload>' + '</ns2:ascframe>';

            var sentData = readyData;
            var zblCar = [];

            _this.OpenURL("POST", "aftersales.i.daimler.com", "/vus/zbl/zbl-connector-web/v1/connector", {
                'Accept': 'application/xml',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'User-Agent': 'Apache-HttpClient/4.5.2 (Java/1.8.0_232)',
                'Connection': 'Keep-Alive',
                'Cookie': AccessCookie[0][0].split(";")[0].replace("SMSESSION=", "sm_session=") + "; ",
                'Authorization': AccessToken["token_type"] + " " + AccessToken["access_token"],
                'DaiHardwareId': DaiHardwareId,
                'DaiApplicationId': DaiApplicationId,
                'Content-Length': sentData.length + "ascDocument=".length
            }, "ascDocument=" + sentData, function (res) {
                //console.log(res);
            }, function (res, data) {
                var response = _this.ab2str(data);
                zblCar.push(response);
            }, function (res) {
                if (res.statusCode == 403) {
                    reject(res.statusMessage);
                    return;
                }
                // ["af:ascframe"]["payload"]["zbl:getVehicleInfoResponse"]
                // -> ["aggregates"]
                // -> ["vehicle"]["ecus"]
                // -> ["vehicle"]["equipmentCodes"]
                // -> ["vehicle"].fin
                // -> ["vehicle"].modelDesignation
                // -> ["vehicle"].series
                // -> 
                var carInfo = decodeURIComponent(zblCar.join(""));
                var carResponse = JSON.parse(xml2jsonParser.toJson(carInfo.replace(/\\"/, "").replace(/\+/g, " ")));
                var vehicleInfoResponse = carResponse["af:ascframe"]["payload"]["zbl:getVehicleInfoResponse"];
                var CarInformation = {
                    "aggregates": vehicleInfoResponse["aggregates"],
                    "ecus": vehicleInfoResponse["vehicle"]["ecus"],
                    "equipmentCodes": vehicleInfoResponse["vehicle"]["equipmentCodes"],
                    "fin": vehicleInfoResponse["vehicle"].fin,
                    "modelDesignation": vehicleInfoResponse["vehicle"].modelDesignation,
                    "series": vehicleInfoResponse["vehicle"].series,
                    "technicalApprovalDate": vehicleInfoResponse["vehicle"].technicalApprovalDate,
                    "ttz": vehicleInfoResponse["vehicle"].ttz

                    //console.log(CarInformation);
                };resolve(CarInformation);
            }, function (res, error) {});
        }
    }, {
        key: '_scnConnector',
        value: function _scnConnector(resolve, reject, vin, category, series, modelDesignation, dateOfTechnicalState, sacodes, ecus, config) {
            var _this = this;

            var sacode = "";
            for (var i = 0; i < sacodes.length; i++) {
                sacode += "<code>" + sacodes[i]["code"] + "</code>";
            }
            /*
            diogenesName
            hwPartNumber
            scnPlant
            swPartNumbers <swPartNumber>1779029706</swPartNumber>
            */

            var diagEcuname = "";
            for (var i = 0; i < ecus.length; i++) {
                if (ecus[i]["diogenesName"] == config.diagName) {
                    diagEcuname = ecus[i];
                }
            }

            console.log(diagEcuname);

            var diogenesName = diagEcuname["diogenesName"];
            var hwPartNumber = diagEcuname["hardwarePartNumber"];
            var scnPlant = diagEcuname["scnPlant"];

            var swPartNumbers = "";

            if (typeof diagEcuname["softwares"].length === "undefined") {
                swPartNumbers = "<swPartNumber>" + diagEcuname["softwares"]["partNumber"] + "</swPartNumber>";
            } else {
                for (var i = 0; i < diagEcuname["softwares"].length; i++) {
                    swPartNumbers += "<swPartNumber>" + diagEcuname["softwares"][i]["partNumber"] + "</swPartNumber>";
                }
            }

            var now = new Date();
            function unixTimestamp(date) {
                if (date === undefined) {
                    date = util.date.getDate();
                }
                return date.getTime() / 1000;
            }

            var readyData = '<?xml+version="1.0"+encoding="UTF-8"+standalone="yes"?>' + '<ns2:ascframe+xsi:type="ns2:serviceRequestFrame"+frameVersion="1.1"+xsi:schemaLocation="http://asconnector.daimler.com/ascFrame+ascFrame_v11.xsd+http://asconnector.daimler.com/common+ascCommon_v11.xsd"+xmlns:ns2="http://asconnector.daimler.com/ascFrame"+xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">' + '    <tracking>' + '        <sessionID>' + vin + '.' + unixTimestamp(now) + '</sessionID>' + '        <requestID>' + vin + '.' + (unixTimestamp(now) + 1) + '</requestID>' + '    </tracking>' + '    <client>' + '        <platform>Open+Shell</platform>' + '        <dateTime>' + moment().add(-10, 'hours').format("YYYY-MM-DDTHH:mm:ss.SSS") + 'Z</dateTime>' + '        <locale>' + '            <language>en</language>' + '            <country>GB</country>' + '        </locale>' + '        <application>' + '            <name>XD</name>' + '            <version>' + XentryVersion + '</version>' + '            <patch></patch>' + '        </application>' + '        <task>' + '            <process>4</process>' + '            <mode>' + DaiHardwareId + '_000000</mode>' + '        </task>' + '    </client>' + '    <destination>' + '        <service>VCS</service>' + '        <action>getMSCN</action>' + '    </destination>' + '    <user>' + '        <userID>' + MBUsername + '</userID>' + '        <organisationIDs>' + '            <organisationID>GB</organisationID>' + '        </organisationIDs>' + '    </user>' + '    <payload>' + '        <ns2:scnRequest+xsi:schemaLocation="http://asconnector.daimler.com/vcs/mscn+VehicleCoding_MSCN_V2.1.0.xsd"+xmlns:ns2="http://asconnector.daimler.com/vcs/mscn">' + '            <version>2.1.0</version>' + '            <statistic>' + '                <category>' + category + '</category>' + '                <series>' + series + '</series>' + '                <modelDesignation>' + modelDesignation + '</modelDesignation>' + '            </statistic>' + '            <finOrVin>' + vin + '</finOrVin>' + '            <dateOfTechnicalState>' + dateOfTechnicalState + '</dateOfTechnicalState>' + '            <testRequest>false</testRequest>' + '            <isSCNV>false</isSCNV>' + '            <codes>' + '                ' + sacode + //<code>705L</code>
            '            </codes>' + '            <ecus>' + '                <ecu>' + '                    <diogenesName>' + diogenesName + '</diogenesName>' + '                    <hwPartNumber>' + hwPartNumber + '</hwPartNumber>' + '                    <scnPlants>' + '                        <scnPlant>' + scnPlant + '</scnPlant>' + '                    </scnPlants>' + '                    <new>' + '                        <swPartNumbers>' + '                            ' + swPartNumbers + //<swPartNumber>1779029706</swPartNumber>
            '                        </swPartNumbers>' + '                    </new>' + '                </ecu>' + '            </ecus>' + '        </ns2:scnRequest>' + '    </payload>' + '</ns2:ascframe>';

            var sentData = readyData;
            var scnCar = [];

            _this.OpenURL("POST", "aftersales.i.daimler.com", "/COBRA_OIDC/asservice/connector", {
                'Accept': 'application/xml',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'User-Agent': 'Apache-HttpClient/4.5.2 (Java/1.8.0_232)',
                'Connection': 'Keep-Alive',
                'Cookie': AccessCookie[0][0].split(";")[0].replace("SMSESSION=", "sm_session=") + "; ",
                'Authorization': AccessToken["token_type"] + " " + AccessToken["access_token"],
                'DaiHardwareId': DaiHardwareId,
                'DaiApplicationId': DaiApplicationId,
                'Content-Length': sentData.length + "ascDocument=".length
            }, "ascDocument=" + sentData, function (res) {
                //console.log(res);
            }, function (res, data) {
                var response = _this.ab2str(data);
                scnCar.push(response);
            }, function (res) {
                var scnInfo = decodeURIComponent(scnCar.join(""));
                var scnResponse = JSON.parse(xml2jsonParser.toJson(scnInfo.replace(/\\"/, "").replace(/\+/g, " ")));
                var scnLog = scnResponse["ns3:ascframe"]["payload"]["ns2:scnResponse"]["ecus"];
                resolve(scnLog);
            }, function (res, error) {});
        }
    }, {
        key: '_flashwareConnector',
        value: function _flashwareConnector(resolve, reject, vin, category, series, modelDesignation, ttz, sacodes, ecus, config) {
            var _this = this;

            var sacode = "";
            for (var i = 0; i < sacodes.length; i++) {
                sacode += sacodes[i]["code"] + (i < sacodes.length - 1 ? "|" : "");
            }

            /*
            diogenesName
            hwPartNumber
            scnPlant
            swPartNumbers <swPartNumber>1779029706</swPartNumber>
            */
            var diagEcuname = "";
            for (var i = 0; i < ecus.length; i++) {
                if (ecus[i]["diogenesName"] == config.diagName) {
                    diagEcuname = ecus[i];
                }
            }

            var diogenesName = diagEcuname["diogenesName"];
            var hwPartNumber = diagEcuname["hardwarePartNumber"];
            var scnPlant = diagEcuname["scnPlant"];

            var swPartNumbers = "";
            if (typeof diagEcuname["softwares"].length === "undefined") {
                swPartNumbers = diagEcuname["softwares"]["partNumber"];
            } else {
                for (var i = 0; i < diagEcuname["softwares"].length; i++) {
                    swPartNumbers += diagEcuname["softwares"][i]["partNumber"] + (i < diagEcuname["softwares"].length - 1 ? "-" : "");
                }
            }

            var now = new Date();
            function unixTimestamp(date) {
                if (date === undefined) {
                    date = util.date.getDate();
                }
                return date.getTime() / 1000;
            }

            var readyData = '<?xml+version="1.0"+encoding="UTF-8"+standalone="yes"?>' + '<ns2:ascframe+xsi:type="ns2:serviceRequestFrame"+frameVersion="1.1"+xsi:schemaLocation="http://asconnector.daimler.com/ascFrame+ascFrame_v11.xsd+http://asconnector.daimler.com/common+ascCommon_v11.xsd"+xmlns:ns2="http://asconnector.daimler.com/ascFrame"+xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">' + '    <tracking>' + '        <sessionID>' + vin + '.' + unixTimestamp(now) + '</sessionID>' + '        <requestID>' + vin + '.' + (unixTimestamp(now) + 1) + '</requestID>' + '    </tracking>' + '    <client>' + '        <platform>Open+Shell</platform>' + '        <dateTime>' + moment().add(-10, 'hours').format("YYYY-MM-DDTHH:mm:ss.SSS") + 'Z</dateTime>' + '        <locale>' + '            <language>en</language>' + '            <country>GB</country>' + '        </locale>' + '        <application>' + '            <name>XD</name>' + '            <version>' + XentryVersion + '</version>' + '            <patch></patch>' + '        </application>' + '        <task>' + '            <process>3</process>' + '            <mode>' + DaiHardwareId + '_000000</mode>' + '        </task>' + '    </client>' + '    <destination>' + '        <service>vpo</service>' + '        <action>calculateFlashware</action>' + '    </destination>' + '    <user>' + '        <userID>D5CTOROK</userID>' + '        <organisationIDs>' + '            <organisationID>GB</organisationID>' + '        </organisationIDs>' + '    </user>' + '    <payload>' + '            <ns2:flashwareCalculationRequest+xsi:schemaLocation="http://asconnector.daimler.com/vponline+flashwareCalculationMessages_V2.4.0.xsd"+xmlns:ns2="http://asconnector.daimler.com/vponline">' + '                <vehicleData>' + '                    <vehicle+category="' + category + '"+model_series="' + series + '"+model_type="' + modelDesignation + '"+vin="' + vin + '"/>' + '                </vehicleData>' + '                <vedocRequest+doRequest="false"/>' + '                <flashData+version="2020.09.020"/>' + '                <dataPool+poolIdentifier=""/>' + '                <finLog>[HEADER]\n' + '    CatalogueFilename=' + IBCatalogue + '\n' + '    CatalogueId=' + CatalogueId + '\n' + '    CatalogueTimestamp=1605456126\n' + '    DiagnosisRelease=' + XentryVersion + '\n' + '    AddOnServerTimestamp=1605467727\n' + '    Baumuster=' + series + '.' + modelDesignation.substring(3, 6) + '\n' + '    LogLevel=0\n' + '    VIN=' + vin + '\n' + '    BR=' + series + '\n' + '    FlashData=2020.09.020\n' + '    SymptomVP=NO\n' + '    SOURCESYSTEM=XD\n' + '    FlashFileExtension=smr-f\n' + '    Sparte=PKW\n' + '    DIEBKZ=N\n' + '    \n' + '    [LoggingInfo]\n' + '    ADDON=5225;5226;16426;16497;16571;16577;16593;16606;16607;16608;16609;16610;16611;16612;16613;16615;16616;16617;16618;16619;16620;16621;16622;16623;16625;16632;16637;16639;16658;16662;16667;16675;16678;16680;16682;16688;16689;16693;16696;16698;16700;16703;16705;16707;16713;16715;16716;16720;16723;16724;16727;16732;16736;16737;16739;16740;16742;16749;16753;16756;16763;16764;16768;16772;16774;16776;16778;16780;16783;16784;16785;16787;16788;16790;16792;16795;16797;16798;16800;16803;16806;16811;16814;16816;16817;16821;16825;16827;16836;16840;16843;16845;16849;16851;16852;16854;16856;16859;16862;16864;16866;16868;16870;16873;16875;16878;16880;16883;16884;16887;16888;16889;16894;16896;16898;16901;16903;16907;16908;16913;16915;16921;16923;16927;16929;16930;16933;16934;16936;16937;16940;16941;16945;16950;16951;16954;16955;16959;16962;16963;16964;16968;16971;16972;16973;16977;16978;16980;16982;16983;16985;16986;16988;16991;16992;16996;16997;17000;17003;17009;17010;17011;17012;17014;17017;17019;17022;17027;17031;17035;17036;17037;17038;17041;17043;17044;17047;17048;17051;17055;17056;17068;17069;17070;17072;17073;17074;17076;17078;17081;17086;17089;17092;17093;17097;17099;17103;17105;17107;17109;17110;17113;17114;17115;17116;17118;17119;17120;17122;17124;17125;17128;17131;17133;17134;17138;17140;17141;17145;17146;17147;17148;17149;17151;17152;17154;17157;17158;17160;17161;17162;17164;17166;17167;17171;17172;17175;17177;17178;17180;17182;17190;17192;17193;17197;17199;17200;17201;17203;17204;17206;17207;17208;17209;17211;17212;17213;17214;17216;17218;17221;17224;17227;17235;17238;17241;17243;17245;17246;17248;17249;17250;17252;17254;17258;17260;17267;17268;17269;17272;17273;17274;17275;17278;17279;17281;17282;17286;17289;17291;17292;17293;17294;17296;17298;17299;17302;17304;17306;17307;17308;17311;17312;17316;17321;17322;17324;17326;17329;17330;17332;17333;17334;17336;17337;17340;17341;17343;17345;17346;17351;17353;17354;17355;17357;17358;17360;17361;17362;17366;17368;17369;17370;17373;17374;17376;17379;17382;17385;17387;17389;17390;17391;17392;17394;17395;17396;17397;17398;17399;17401;17403;17404;17405;17406;17409;17412;17419;17421;17426;17431;17432;17434;17438;17440;17441;17445;17449;17453;17455;17463;17467;17469;17470;17471;17473;17475;17478;17479;17480;17484;17486;17487;17491;17493;17497;\n' + '    SessionID=' + vin + '.' + unixTimestamp(now) + '\n' + '    BusinessType=\n' + '    BusinessPath=\n' + '    DeviceID=' + DaiHardwareId + '\n' + '    DevicePlatform=OpenShell\n' + '    DeviceComputername=OpenShell\n' + '    Application=XD\n' + '    \n' + '    [ATTRIB]\n' + '    BM=' + series + '.' + modelDesignation.substring(3, 6) + '\n' + '    \n' + '    [ETN]\n' + '    FIN_Vehicle=' + vin + '\n' + '    FIN_Internal=' + vin + '\n' + '    TTZ=' + ttz + '\n' + '    SACode=' + sacode + '\n' + '    \n' + '    [ECU1]\n' + '    SG_DiogName=' + diogenesName + '\n' + '    SG_REFNR=' + (config.useVedoc ? hwPartNumber : config.hwid) + '\n' + '    FW_MBS1=\n' + '    SG_Current_SW=\n' + '    SG_EXCHANGE=0\n' + '    VP_Rate_Modules=ALLMODULES\n' + '    \n' + '    </finLog>' + '            </ns2:flashwareCalculationRequest>' + '        </payload>' + '    </ns2:ascframe>';

            var sentData = readyData;
            var flashwareCar = [];

            _this.OpenURL("POST", "aftersales.i.daimler.com", "/vus/flashwarecalculation/vponline-connector-web/v1/connector", {
                'Accept': 'application/xml',
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'User-Agent': 'Apache-HttpClient/4.5.2 (Java/1.8.0_232)',
                'Connection': 'Keep-Alive',
                'Cookie': AccessCookie[0][0].split(";")[0].replace("SMSESSION=", "sm_session=") + "; ",
                'Authorization': AccessToken["token_type"] + " " + AccessToken["access_token"],
                'DaiHardwareId': DaiHardwareId,
                'DaiApplicationId': DaiApplicationId,
                'Content-Length': sentData.length + "ascDocument=".length
            }, "ascDocument=" + sentData, function (res) {
                //console.log(res);
            }, function (res, data) {
                var response = _this.ab2str(data);
                flashwareCar.push(response);
            }, function (res) {
                var flashwareInfo = decodeURIComponent(flashwareCar.join(""));
                var flashwareResponse = JSON.parse(xml2jsonParser.toJson(flashwareInfo.replace(/\\"/, "").replace(/\+/g, " ")));
                var flashwareLog = flashwareResponse["af:ascframe"]["payload"]["v:flashwareCalculationResponse"]["finLog"];

                /*
                {
                    "ecurefer":flashwares.ECU1.ECU_Refer,
                    "ecuname":flashwares.ECU1.SG_DiogName,
                    "hwid":flashwares.ECU1.SG_REFNR,
                    "currentsw":flashwares.ECU1.SG_Current_SW,
                    "fittingsw":flashwares.ECU1.SG_FITTINGFLASHSW,
                    "newsw":flashwares.ECU1.SG_NEWFLASHSW,
                    "sacode":flashwares.ETN.SACode
                }                
                */

                resolve(flashwareLog);
            }, function (res, error) {});
        }
    }, {
        key: '_loginMBServer',
        value: function _loginMBServer(resolve, reject) {
            var _this = this;
            Cookie = [];
            AccessToken = [];
            AccessCookie = [];

            try {
                _this.OpenURL("GET", 'xdiag-aftersales.i.daimler.com', '/xdiag/uis/loggedOk', {
                    'User-Agent': 'WLML/4'
                }, "", function (res) {
                    try {
                        _this.getToken(resolve, reject, res);
                    } catch (e) {
                        reject(e);
                    }
                }, function (res, data) {}, function (res) {}, function (res, error) {});
            } catch (e) {
                reject(e);
            }
        }
    }, {
        key: 'getToken',
        value: function getToken(resolve, reject, res) {
            var _this = this;

            //console.log(res.headers.location);
            Cookie.push(res.headers["set-cookie"]);
            var location = res.headers.location.split("/?")[1];

            // Get Token
            _this.OpenURL("GET", "login.daimler.com", "/?" + location, {
                'User-Agent': 'WLML/4'
            }, "", function (res) {
                Cookie.push(res.headers["set-cookie"]);
                var postData = [];
                postData.push("_csrf=" + Cookie[1][0].split(";")[0].replace("XSRF-TOKEN=", ""));
                postData.push("smTarget=https%3A%2F%2Fxdiag-aftersales.i.daimler.com%3A443%2Fxdiag%2Fuis%2FloggedOk");
                postData.push("userid=");
                postData.push("username=" + MBUsername);
                postData.push("password=" + MBPassword);
                var readyData = postData.join('&');

                // try to login with logins
                _this.OpenURL("POST", "login.daimler.com", "/", {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'User-Agent': 'WLML/4',
                    'Connection': 'Keep-Alive',
                    'Cookie': Cookie[1][0].split(";")[0] + "; " + Cookie[1][1].split(";")[0],
                    'Content-Length': readyData.length
                }, readyData, function (res) {
                    Cookie.push(res.headers["set-cookie"]);
                    console.log("User is logging in..");
                    //console.log(res.headers.location);

                    // Open Session
                    if (res.headers.location == null) {
                        return;
                    }
                    var location = res.headers.location.split("/cp?")[1];
                    _this.OpenURL("GET", "session.i.daimler.com", "/cp?" + location, {
                        'User-Agent': 'WLML/4'
                    }, "", function (res) {
                        Cookie.push(res.headers["set-cookie"]);
                        var postData = [];
                        postData.push("response_type=code");
                        postData.push("client_id=8c9ead97-9198-43d4-8dec-a0d6b6dbd32b");
                        postData.push("scope=openid");
                        postData.push("redirect_uri=https%3A%2F%2Fxdiag-aftersales.i.daimler.com%3A443%2Fxdiag%2Fuis%2FloggedOk");
                        postData.push("state=53cc9b56ff8010f69b29861f96d62f42b87e286bb4aeecc426d220b1409dc378");
                        postData.push("code_challenge=lBgxkeWa5qDkKAYcIZVsduoVTjmDPf-KxfDj5eOb5n8");
                        postData.push("code_challenge_method=S256");
                        var readyData = postData.join('&');

                        // oauth2
                        _this.OpenURL("POST", "sso.daimler.com", "/as/authorization.oauth2", {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'User-Agent': 'WLML/4',
                            'Connection': 'Keep-Alive',
                            'Content-Length': readyData.length
                        }, readyData, function (res) {
                            Cookie.push(res.headers["set-cookie"]);

                            // login daimler
                            var location = res.headers.location.split("/?")[1];
                            _this.OpenURL("GET", "login.daimler.com", "/?" + location, {
                                'User-Agent': 'WLML/4',
                                'Cookie': Cookie[2][0].split(";")[0] + "; " + Cookie[2][1].split(";")[0] + "; " + Cookie[1][0].split(";")[0] + "; " + Cookie[1][1].split(";")[0]
                            }, "", function (res) {
                                Cookie.push(res.headers["set-cookie"]);

                                // SSO Daimler Authoriation
                                var location = res.headers.location.split("com/")[1];
                                _this.OpenURL("GET", "sso.daimler.com", "/" + location, {
                                    'User-Agent': 'WLML/4',
                                    'Cookie': Cookie[4][0].split(";")[0] + "; " + Cookie[4][1].split(";")[0] + "; " + Cookie[4][2].split(";")[0] + "; " + Cookie[5][1].split(";")[0] + "; " + Cookie[5][2].split(";")[0]
                                }, "", function (res) {
                                    Cookie.push(res.headers["set-cookie"]);
                                    if (typeof res.headers.location === "undefined") {
                                        reject(res);
                                    }
                                    var code = res.headers.location.split('?')[1].split('&')[0];
                                    var postData = [];
                                    postData.push("grant_type=authorization_code");
                                    postData.push("client_id=8c9ead97-9198-43d4-8dec-a0d6b6dbd32b");
                                    postData.push("scope=openid");
                                    postData.push("redirect_uri=https%3A%2F%2Fxdiag-aftersales.i.daimler.com%3A443%2Fxdiag%2Fuis%2FloggedOk");
                                    postData.push("code_verifier=bc9d6dd4db297589c0f8fcd6c28e2bbab3d087be9d7e680dd4ebf451a42e9640");
                                    postData.push(code);
                                    var readyData = postData.join('&');

                                    // Get Access Token
                                    _this.OpenURL("POST", "sso.daimler.com", "/as/token.oauth2", {
                                        'Content-Type': 'application/x-www-form-urlencoded',
                                        'User-Agent': 'WLML/4',
                                        'Connection': 'Keep-Alive',
                                        'Cookie': Cookie[4][0].split(";")[0] + "; " + Cookie[4][1].split(";")[0] + "; " + Cookie[4][2].split(";")[0] + "; ",
                                        'Content-Length': readyData.length
                                    }, readyData, function (res) {
                                        Cookie.push(res.headers["set-cookie"]);
                                    }, function (res, data) {
                                        // Recevied Auth Token
                                        //AccessToken.push(ab2str(data));
                                        AccessToken = JSON.parse(_this.ab2str(data));

                                        // Authorize Client
                                        _this.OpenURL("GET", "cebas-pki.i.daimler.com", "/api/auth/v1/authorize?client_id=2BH7ytGIgx&redirect_uri=https%3A%2F%2Fcebas-pki.i.daimler.com%2Fapi%2Fauth%2Fv1%2Fauthorize&response_type=token&scope=credential&state=1", {
                                            'User-Agent': 'WLML/4',
                                            'Cookie': Cookie[3][1].split(";")[0] + "; " + Cookie[3][1].split(";")[0].replace("SMSESSION=", "sm_session=")
                                        }, "", function (res) {
                                            Cookie.push(res.headers["set-cookie"]);
                                            AccessAuthorization = AccessToken["token_type"] + " " + AccessToken["access_token"];

                                            // Get UserInformation
                                            _this.OpenURL("GET", "xdiag-aftersales.i.daimler.com", "/xdiag/uis/UserInformation", {
                                                'Cookie': Cookie[3][1].split(";")[0] + "; " + Cookie[3][1].split(";")[0].replace("SMSESSION=", "sm_session=") + ";" + Cookie[4][2].split(";")[0] + "; ",
                                                'Authorization': AccessToken["token_type"] + " " + AccessToken["access_token"],
                                                'DaiHardwareId': DaiHardwareId,
                                                'DaiApplicationId': DaiApplicationId
                                            }, "", function (res) {
                                                Cookie.push(res.headers["set-cookie"]);
                                                AccessCookie.push(res.headers["set-cookie"]);

                                                // Get User State
                                                _this.OpenURL("GET", "xdiag-aftersales.i.daimler.com", "/xdiag/uis/loggedOk", {
                                                    'Cookie': Cookie[9][0].split(";")[0] + "; " + Cookie[9][0].split(";")[0].replace("SMSESSION=", "sm_session=") + ";" + Cookie[9][1].split(";")[0] + "; " + Cookie[9][2].split(";")[0] + "; ",
                                                    'Authorization': AccessToken["token_type"] + " " + AccessToken["access_token"],
                                                    'DaiHardwareId': DaiHardwareId,
                                                    'DaiApplicationId': DaiApplicationId
                                                }, "", function (res) {
                                                    Cookie.push(res.headers["set-cookie"]);

                                                    // Get Company Information
                                                    _this.OpenURL("GET", "xdiag-aftersales.i.daimler.com", "/xdiag/uis/queryCompanyInfo", {
                                                        'Cookie': Cookie[9][0].split(";")[0] + "; " + Cookie[9][0].split(";")[0].replace("SMSESSION=", "sm_session=") + ";" + Cookie[9][1].split(";")[0] + "; " + Cookie[9][2].split(";")[0] + "; ",
                                                        'Authorization': AccessToken["token_type"] + " " + AccessToken["access_token"],
                                                        'DaiHardwareId': DaiHardwareId,
                                                        'DaiApplicationId': DaiApplicationId
                                                    }, "", function (res) {
                                                        Cookie.push(res.headers["set-cookie"]);
                                                        resolve(res);
                                                    }, function (res, data) {
                                                        CompanyInformation = _this.ab2str(data);
                                                        //console.log(CompanyInformation);
                                                    }, function (res, error) {});
                                                }, function (res, data) {
                                                    // Logging State
                                                    console.log(_this.ab2str(data));
                                                }, function (res) {}, function (res, error) {});
                                            }, function (res, data) {
                                                UserInformation = _this.ab2str(data);
                                                //console.log(UserInformation);
                                            }, function (res) {}, function (res, error) {});
                                        }, function (res, data) {
                                            //console.log(_this.ab2str(data));
                                        }, function (res) {}, function (res, error) {});
                                    }, function (res) {}, function (res, error) {});
                                }, function (res, data) {}, function (res) {}, function (res, error) {});
                            }, function (res, data) {}, function (res) {}, function (res, error) {});
                        }, function (res, data) {}, function (res) {}, function (res, error) {});
                    }, function (res, data) {}, function (res) {}, function (res, error) {});
                }, function (res, data) {
                    reject("Please check your user name and password.");
                    //console.log(_this.ab2str(data));
                }, function (res) {}, function (res, error) {
                    console.log(error);
                });
            }, function (res, data) {
                //console.log(_this.ab2str(data1));
            }, function (res) {}, function (res, error) {});
        }
    }]);

    return MBAdaptor;
}();

exports.default = MBAdaptor;