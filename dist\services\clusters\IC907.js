"use strict";
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _moment = require("moment");

var _moment2 = _interopRequireDefault(_moment);

var _axios = require("axios");

var _axios2 = _interopRequireDefault(_axios);

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

var _mbClusterManagerBase = require("../mbClusterManagerBase");

var _mbClusterManagerBase2 = _interopRequireDefault(_mbClusterManagerBase);

var _utils = require("../../utils");

var _utils2 = _interopRequireDefault(_utils);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return call && (typeof call === "object" || typeof call === "function") ? call : self; }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function, not " + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }

var IC907 = function (_MBClusterManagerBase) {
    _inherits(IC907, _MBClusterManagerBase);

    function IC907(dbConnector) {
        _classCallCheck(this, IC907);

        return _possibleConstructorReturn(this, (IC907.__proto__ || Object.getPrototypeOf(IC907)).call(this, dbConnector));
    }

    _createClass(IC907, [{
        key: "Virginize",
        value: function Virginize() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x01 + "], \"reply\": [" + 0x50 + "] }," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x22 + "," + 0xF1 + "," + 0x11 + "], \"reply\": [" + 0x62 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x23 + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0x00 + "," + 0x20 + "], \"reply\": [" + 0x63 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x23 + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x75 + "," + 0x20 + "], \"reply\": [" + 0x63 + "] }," + "            { \"proc\": \"push\" }," +
            //        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0xF0 + "," + 0x29 + "," + 0x01 + "], \"reply\": [" + 0x71 + "," + 0x01 + "," + 0xF0 + "," + 0x29 + "] }," +
            //        "            { \"proc\": \"push\" }," +
            //        "            { \"proc\": \"sgkt\", \"param\": [\"level4\"], \"reply\": [] }," +
            "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x08 + "," + 0x01 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x08 + "," + 0x02 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x08 + "," + 0x06 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x03 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
            //        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x03 + "," + 0x1C + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
            "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x04 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x04 + "," + 0x04 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x08 + "," + 0x08 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x10 + "," + 0x20 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x30 + "," + 0x10 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x40 + "," + 0x0C + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0xFE + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x24 + "," + 0xD4 + "," + 0x0C + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x24 + "," + 0xE0 + "," + 0x10 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x24 + "," + 0xF0 + "," + 0x10 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x25 + "," + 0x00 + "," + 0x10 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x07 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," +
            //        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x03 + "," + 0x07 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
            "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x03 + "," + 0x05 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x03 + "," + 0x05 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "EpromEdit",
        value: function EpromEdit() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x01 + "], \"reply\": [" + 0x50 + "] }," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x22 + "," + 0xF1 + "," + 0x11 + "], \"reply\": [" + 0x62 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x23 + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0x00 + "," + 0x20 + "], \"reply\": [" + 0x63 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x23 + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x75 + "," + 0x20 + "], \"reply\": [" + 0x63 + "] }," + "            { \"proc\": \"push\" }" +
            //        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0xF0 + "," + 0x29 + "," + 0x01 + "], \"reply\": [" + 0x71 + "," + 0x01 + "," + 0xF0 + "," + 0x29 + "] }," +
            //        "            { \"proc\": \"push\" }," +
            //        "            { \"proc\": \"sgkt\", \"param\": [\"level4\"], \"reply\": [] }" +
            "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "Unlock",
        value: function Unlock() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x01 + "], \"reply\": [" + 0x50 + "] }," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x22 + "," + 0xF1 + "," + 0x11 + "], \"reply\": [" + 0x62 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x23 + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0x00 + "," + 0x20 + "], \"reply\": [" + 0x63 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x23 + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x75 + "," + 0x20 + "], \"reply\": [" + 0x63 + "] }," + "            { \"proc\": \"push\" }" +
            //        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0xF0 + "," + 0x29 + "," + 0x01 + "], \"reply\": [" + 0x71 + "," + 0x01 + "," + 0xF0 + "," + 0x29 + "] }," +
            //        "            { \"proc\": \"push\" }," +
            //        "            { \"proc\": \"sgkt\", \"param\": [\"level4\"], \"reply\": [] }" +
            "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "DisableEZSSync",
        value: function DisableEZSSync() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x01 + "], \"reply\": [" + 0x50 + "] }," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x22 + "," + 0xF1 + "," + 0x11 + "], \"reply\": [" + 0x62 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x23 + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0x00 + "," + 0x20 + "], \"reply\": [" + 0x63 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x23 + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x75 + "," + 0x20 + "], \"reply\": [" + 0x63 + "] }," + "            { \"proc\": \"push\" }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0xF0 + "," + 0x29 + "," + 0x01 + "], \"reply\": [" + 0x71 + "," + 0x01 + "," + 0xF0 + "," + 0x29 + "] }," + "            { \"proc\": \"push\" }," +
            //        "            { \"proc\": \"sgkt\", \"param\": [\"level4\"], \"reply\": [] }," +
            //        "            { \"proc\": \"sleep\", \"param\": 2000}," +
            "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x08 + "," + 0x08 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x08 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x10 + "," + 0x08 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x18 + "," + 0x01 + "," + 0xAA + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}" + "        ]" + "}";
            return CMDResponse;
        }
    }]);

    return IC907;
}(_mbClusterManagerBase2.default);

exports.default = IC907;