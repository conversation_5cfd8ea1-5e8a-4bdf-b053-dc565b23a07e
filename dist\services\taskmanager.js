"use strict";

module.exports = function () {

    var fs = require('fs');
    var Cache = require('cache-base');
    var taskPool = new Cache('data', {});
    var taskFile = __dirname + '/banks/tasks.json';
    //
    // Task Item Class
    //
    function TaskItem(title) {
        this.id = 0;
        this.title = title || "";
        this.process = "";
        this.timeOut = 3000; //ms
        this.delay = 0; //ms
        this.response = -1; // ok
        this.taskId = 0;
        this.enableDebug = true;
        this.running = false;
        this.enable = true;
        this.lastStateChange = 0;
        this.payload = {};
    }

    TaskItem.prototype.setTitle = function (title) {
        this.title = title;
    };

    TaskItem.prototype.setProcess = function (process) {
        this.process = process;
    };

    TaskItem.prototype.setRunning = function (running) {
        this.running = running;
    };

    TaskItem.prototype.setTimeOut = function (timeOut) {
        this.timeOut = timeOut;
    };

    TaskItem.prototype.setPayload = function (data) {
        this.payload = data;
    };

    TaskItem.prototype.getPayload = function () {
        return this.payload;
    };

    TaskItem.prototype.setResponse = function (response) {
        this.response = response;
    };

    TaskItem.prototype.setLastStateChange = function (lastStateChange) {
        this.lastStateChange = lastStateChange || getCurrentTime();
    };

    TaskItem.prototype.setWorker = function (callback) {
        this.workerCallback = callback;
    };

    TaskItem.prototype.setDelay = function (delay) {
        this.delay = delay;
    };

    TaskItem.prototype.worker = function () {
        var self = this;
        if (this.running) return false;

        this.running = true;

        if (this.workerCallback) this.workerCallback(self);
    };

    TaskItem.prototype.start = function () {
        var _this = this;

        var self = this;
        this.response = -1;
        if (this.delay > 0) {
            setTimeout(function () {
                self.id = setInterval(function () {
                    return _this.worker();
                }, _this.timeOut);
            }, this.delay);
        } else {
            this.id = setInterval(function () {
                return _this.worker();
            }, this.timeOut);
        }
        this.lastStateChange = getCurrentTime();
        this.debug("is started");
    };

    TaskItem.prototype.stop = function () {
        clearTimeout(this.id);
        this.running = false;
        this.lastStateChange = getCurrentTime();
        this.debug("is stopped.");
    };

    TaskItem.prototype.debug = function (msg) {
        if (this.enableDebug) console.log("Worker @" + this.taskId + " " + msg);
    };
    module.exports = TaskItem;
    // End of Class


    function init() {
        console.log("Task Manager Initiliazed.");
    }

    function getCurrentTime() {
        return new Date().valueOf();
    }

    function createTask(task) {
        var taskId = new Date().valueOf() + "_" + makeid(16);
        taskPool.set("worker_" + taskId, task);
        task.taskId = taskId;
        task.start();
    }

    function taskList() {
        var tasks = {};
        for (var task in taskPool.data) {
            tasks[task] = {
                "title": taskPool.data[task].title,
                "response": taskPool.data[task].response,
                "process": taskPool.data[task].process,
                "timeout": taskPool.data[task].timeOut,
                "delay": taskPool.data[task].delay,
                "running": taskPool.data[task].running,
                "enable": taskPool.data[task].enable,
                "payload": taskPool.data[task].payload,
                "laststatechange": taskPool.data[task].lastStateChange
            };
        }

        return tasks;
    }

    function removeTask(taskId) {
        if (!taskPool.has(taskId)) {
            return 0;
        }
        var taskItem = taskPool.get(taskId);
        taskItem.stop();
        taskPool.del(taskId);
        return 1;
    }

    function clearTasks() {
        var i = 0;
        for (var task in taskPool.data) {
            var taskItem = taskPool.get(task);
            taskItem.stop();
            taskPool.del(task);
            i++;
        }
        return i;
    }

    function startTasks() {
        var i = 0;
        for (var task in taskPool.data) {
            var taskItem = taskPool.get(task);
            taskItem.start();
            i++;
        }
        return i;
    }

    function stopTasks() {
        var i = 0;
        for (var task in taskPool.data) {
            var taskItem = taskPool.get(task);
            taskItem.stop();
            i++;
        }
        return i;
    }

    function enableTask(taskid) {
        if (!taskPool.has(taskid)) {
            return 0;
        }
        var taskItem = taskPool.get(taskid);
        taskItem.start();
        return 1;
    }

    function disableTask(taskid) {
        if (!taskPool.has(taskid)) {
            return 0;
        }
        var taskItem = taskPool.get(taskid);
        taskItem.stop();
        return 1;
    }

    function updateTask(taskid, title) {
        if (!taskPool.has(taskid)) {
            return 0;
        }
        var taskItem = taskPool.get(taskid);
        taskItem.setTitle(title);
        return 1;
    }

    function getTask(taskid) {
        if (!taskPool.has(taskid)) {
            return 0;
        }
        var taskItem = taskPool.get(taskid);
        return {
            "title": taskItem.title,
            "response": taskItem.response,
            "process": taskItem.process,
            "taskid": taskItem.taskId,
            "timeout": taskItem.timeOut,
            "delay": taskItem.delay,
            "enable": taskItem.enable,
            "running": taskItem.running,
            "payload": taskItem.payload,
            "laststatechange": taskItem.lastStateChange
        };
    }

    function getTaskInfo() {
        var info = { 'error': 0, 'running': 0, 'disabled': 0 };

        var count = 0;
        for (var task in taskPool.data) {
            if (taskPool.data[task].response == -1) {
                info.error++;
            }
            if (taskPool.data[task].running) {
                info.running++;
            }
            count++;
        }

        info.disabled = count - info.running;

        return info;
    }

    function makeid(length) {
        var result = '';
        var characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        var charactersLength = characters.length;
        for (var i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * charactersLength));
        }
        return result;
    }

    init();
    //
    // Export Functions
    //
    return {
        TaskItem: TaskItem,
        createTask: createTask,
        removeTask: removeTask,
        startTasks: startTasks,
        stopTasks: stopTasks,
        taskList: taskList,
        clearTasks: clearTasks,
        enableTask: enableTask,
        disableTask: disableTask,
        updateTask: updateTask,
        getTaskInfo: getTaskInfo,
        getTask: getTask
    };
};