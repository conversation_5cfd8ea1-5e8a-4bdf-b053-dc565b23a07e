"use strict";
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _moment = require("moment");

var _moment2 = _interopRequireDefault(_moment);

var _axios = require("axios");

var _axios2 = _interopRequireDefault(_axios);

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

var _mbClusterManagerBase = require("../mbClusterManagerBase");

var _mbClusterManagerBase2 = _interopRequireDefault(_mbClusterManagerBase);

var _utils = require("../../utils");

var _utils2 = _interopRequireDefault(_utils);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return call && (typeof call === "object" || typeof call === "function") ? call : self; }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function, not " + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }

var IC222 = function (_MBClusterManagerBase) {
    _inherits(IC222, _MBClusterManagerBase);

    function IC222(dbConnector) {
        _classCallCheck(this, IC222);

        return _possibleConstructorReturn(this, (IC222.__proto__ || Object.getPrototypeOf(IC222)).call(this, dbConnector));
    }

    _createClass(IC222, [{
        key: "AMG360KM",
        value: function AMG360KM() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x02 + "," + 0x97 + "," + 0x01 + "," + 0xEF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x07 + "," + 0xD9 + "," + 0x01 + "," + 0x76 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0x93 + "," + 0x02 + "," + 0x20 + "," + 0x16 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMG330KM",
        value: function AMG330KM() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x02 + "," + 0x97 + "," + 0x01 + "," + 0xEE + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x07 + "," + 0xD9 + "," + 0x01 + "," + 0x12 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0x93 + "," + 0x02 + "," + 0x20 + "," + 0x54 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGEnable",
        value: function AMGEnable() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0x93 + "," + 0x02 + "," + 0x20 + "," + 0x54 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGEnable205",
        value: function AMGEnable205() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0x93 + "," + 0x02 + "," + 0x20 + "," + 0x16 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0xA1 + "," + 0x01 + "," + 0x20 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGDisable",
        value: function AMGDisable() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x02 + "," + 0x97 + "," + 0x01 + "," + 0xEE + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x07 + "," + 0xD9 + "," + 0x01 + "," + 0x12 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x08 + "," + 0x93 + "," + 0x02 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "CheckVirginizable",
        value: function CheckVirginizable() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x22 + "," + 0xF1 + "," + 0x00 + "], \"reply\": [" + 0x62 + "," + 0xF1 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x22 + "," + 0x00 + "," + 0x01 + "], \"reply\": [" + 0x62 + "," + 0x00 + "," + 0x01 + "] }," + "            { \"proc\": \"push\" }," + "            { \"proc\": \"findaddress\", \"param\": {\"start\":\"0xFEDD7C00\",\"end\":\"0xFEDD7F00\"} }," + "            { \"proc\": \"pop\" }," + "            { \"proc\": \"checkwritable\" }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "VirginMethod3",
        value: function VirginMethod3() {
            // 0x3D, 0x14, 0x02, 0x00, 0x01, 0x9D, 0x04, 0xFE, 0xFF, 0xFF, 0xFF
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," +
            // SSID       "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x01 + "," + 0x95 + "," + 0x08 + "," + 0x00 + "," + 0x00 +"," + 0x00 +"," + 0x00 +"," + 0x00 +"," + 0x00 +"," + 0x00 +"," + 0x00 +"], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
            "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x01 + "," + 0x9D + "," + 0x04 + "," + 0xFE + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x23 + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x02 + "," + 0xD9 + "," + 0x01 + "], \"reply\": [" + 0x63 + "] }," + "            { \"proc\": \"push\" }," + "            { \"proc\": \"setbit\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x02 + "," + 0xD9 + "," + 0x01 + "], \"mask\": [" + 0x80 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x07 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"clearbit\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x02 + "," + 0xD9 + "," + 0x01 + "], \"mask\": [" + 0x7F + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "VirginMethod2",
        value: function VirginMethod2() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0xF0 + "," + 0x0B + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x07 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "VirginMethod1",
        value: function VirginMethod1() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x22 + "," + 0xF1 + "," + 0x00 + "], \"reply\": [" + 0x62 + "," + 0xF1 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x22 + "," + 0x00 + "," + 0x01 + "], \"reply\": [" + 0x62 + "," + 0x00 + "," + 0x01 + "] }," + "            { \"proc\": \"push\" }," + "            { \"proc\": \"findaddress\", \"param\": {\"start\":\"0xFEDD7C00\",\"end\":\"0xFEDD7F00\"} }," + "            { \"proc\": \"pop\" }," + "            { \"proc\": \"checkwritable\" }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x01 + "," + 0x95 + "," + 0x08 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x01 + "," + 0x9D + "," + 0x04 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
            //        "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x01 + "," + 0x91 + "," + 0x04 + "," + 0x00 + "," + 0x00 +"," + 0x00 +"," + 0x00 +"], \"reply\": [" + 0x7D + "," + 0x14 + "] }," +
            "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x14 + "," + 0x02 + "," + 0x00 + "," + 0x02 + "," + 0xE2 + "," + 0x02 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x14 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x07 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x02 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
            //        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x08 + "," + 0x01 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
            //        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x08 + "," + 0x02 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
            //        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x08 + "," + 0x06 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
            //        "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x15 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," +
            "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x01 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0x02 + "," + 0x00 + "," + 0x09 + "], \"reply\": [" + 0x71 + "," + 0x02 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "EpromEdit",
        value: function EpromEdit() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "Unlock",
        value: function Unlock() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }," + "            { \"proc\": \"securityinit\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }" + "        ]" + "}";
            return CMDResponse;
        }
    }]);

    return IC222;
}(_mbClusterManagerBase2.default);

exports.default = IC222;