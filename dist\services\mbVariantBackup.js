"use strict";
/*************************
    MB Variant Backup
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var MBVariantBackup = function () {
    function MBVariantBackup(dbConnector) {
        _classCallCheck(this, MBVariantBackup);

        this.db = dbConnector;

        this.createTokenId = this.createTokenId.bind(this);
        this.backup = this.backup.bind(this);
        this.variants = this.variants.bind(this);
    }

    _createClass(MBVariantBackup, [{
        key: "createTokenId",
        value: function createTokenId(hwid, swid) {
            return (0, _md2.default)(hwid + swid);
        }
    }, {
        key: "variants",
        value: function variants(ecu, callback) {
            var _this = this;
            var SQLquery = { query: "CALL ecuVariants(?);", params: [ecu] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[0]);
            });
        }
    }, {
        key: "backup",
        value: function backup(userid, ecu, cid, serial, hwid, swid, diagname, variants, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL ecuVariantBackup(?,?,?,?,?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, ecu, cid, serial, hwid, swid, diagname, variants] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }, {
        key: "check",
        value: function check(userid, ecu, serial, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL ecuVariantsCheck(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, ecu, serial] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }, {
        key: "get",
        value: function get(userid, ecu, serial, callback) {
            var _this = this;
            var SQLquery = { query: "CALL ecuVariantsGet(?,?,?);", params: [userid, ecu, serial] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[0]);
            });
        }
    }, {
        key: "error",
        value: function error(userid, ecu, codes, callback) {
            var _this = this;
            var SQLquery = { query: "CALL ecuErrorCheck(?,?,?);", params: [userid, ecu, codes] };
            _this.db.QueryExec(SQLquery, function (result) {
                console.log(result);
                callback(result[0]);
            });
        }
    }]);

    return MBVariantBackup;
}();

exports.default = MBVariantBackup;