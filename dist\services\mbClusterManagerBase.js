"use strict";
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _moment = require("moment");

var _moment2 = _interopRequireDefault(_moment);

var _axios = require("axios");

var _axios2 = _interopRequireDefault(_axios);

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

var _cryptoJs = require("crypto-js");

var _cryptoJs2 = _interopRequireDefault(_cryptoJs);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var fs = require('fs');
var xml2jsonParser = require('xml2json');
var ini = require('ini');

var MBClusterManagerBase = function MBClusterManagerBase(dbConnector) {
    _classCallCheck(this, MBClusterManagerBase);

    this.db = dbConnector;
};

exports.default = MBClusterManagerBase;