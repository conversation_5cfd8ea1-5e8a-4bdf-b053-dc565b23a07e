"use strict";
/*************************
    MB Services Classes
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

var _EZS = require("./eis/EZS213");

var _EZS2 = _interopRequireDefault(_EZS);

var _EZS3 = require("./eis/EZS167");

var _EZS4 = _interopRequireDefault(_EZS3);

var _EIS = require("./eis/EIS447");

var _EIS2 = _interopRequireDefault(_EIS);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var MBEISManager = function () {
    function MBEISManager(dbConnector) {
        _classCallCheck(this, MBEISManager);

        this.db = dbConnector;

        this.EZS213 = new _EZS2.default(this.db);
        this.EZS167 = new _EZS4.default(this.db);
        this.EIS447 = new _EIS2.default(this.db);

        this.createTokenId = this.createTokenId.bind(this);
        this.tokenAdd = this.tokenAdd.bind(this);
    }

    _createClass(MBEISManager, [{
        key: "createTokenId",
        value: function createTokenId(hwid, swid) {
            return (0, _md2.default)(hwid + swid);
        }
    }, {
        key: "tokenCheck",
        value: function tokenCheck(userid, cid, sku, hwid, swid, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenCheck(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, (0, _md2.default)(cid + sku + hwid + swid), sku] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }, {
        key: "tokenAdd",
        value: function tokenAdd(userid, cid, sku, hwid, swid, state, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenAdd(?,?,?,?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, (0, _md2.default)(cid + sku + hwid + swid), cid, sku, hwid, swid, state] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }, {
        key: "tokenConfirm",
        value: function tokenConfirm(userid, cid, sku, hwid, swid, state, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenConfirm(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, (0, _md2.default)(cid + sku + hwid + swid), state] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }]);

    return MBEISManager;
}();

exports.default = MBEISManager;