/*
    MBlink Service
    Coded By <PERSON><PERSON>er
*/
require('dotenv-extended').load();

// Load Libraries
import 'babel-polyfill';
import createError from "http-errors";
import Express from "express";
import session from "express-session";
import fs from "fs";    
import http from "http";
import {URL} from "url";
import BodyParser from "body-parser";
import DBConnector from "./dbconnector";
import MBServices from "./services";
import utils from "./utils";
import md5 from 'md5';
import { Console } from 'console';
var cookieParser = require('cookie-parser');
var cookieSession = require('cookie-session');

var sessionStore = new session.MemoryStore();

// Initiliaze Libraries
let wwwServer=Express();
let db = new DBConnector();
let mbservices = new MBServices(db);

const wwwRootFolder = __dirname+"/wwwroot";

function setWebServer() {

    wwwServer.use(BodyParser.json({limit: "500mb"}));
    wwwServer.use(BodyParser.urlencoded({extended: false, limit: "500mb"}));    

    // gzip/deflate outgoing responses
    var compression = require('compression');
    wwwServer.use(compression());
    wwwServer.use(cookieParser());
    wwwServer.set('trust proxy', 1);

    // store session state   
    wwwServer.use(session({
        store: sessionStore,
        key: 'mblink_sid',
        secret: 'AstronBnXMbLinkCluster',
        resave: true,
        saveUninitialized: false,
        rolling: true,
        cookie: {
            //sameSite: 'none',
            //secure: true,
            expires: 30*3600*1000 // 30 Hours
        }
    }));

    // No cache
    wwwServer.use(function (req, res, next) {

        var allowedOrigins = [
            "http://localhost:5000",
            "http://localhost:5000/",
            "http://127.0.0.1:5000",                             
            "http://localhost:3000", 
            "http://127.0.0.1:3011", 
            "http://localhost:81", 
            "http://localhost:80",             
            "http://mb-link.com:81", 
            "http://www.mb-link.com:80", 
            "http://home.bicer.me:3011",
            "http://store.mb-link.com", 
            "http://cluster.mb-link.com",
            "http://store.mb-link.com:80", 
            "http://cluster.mb-link.com:80",
            "https://store.mb-link.com", 
            "https://store.mb-link.com/", 
            "https://www.mbnavicodes.com/", 
            "http://www.mbnavicodes.com/", 
            "https://mbnavicodes.com/", 
            "http://mbnavicodes.com/", 
            "https://www.mbnavicodes.com", 
            "http://www.mbnavicodes.com", 
            "http://www.mbnavicodes.com:80", 
            "https://mbnavicodes.com", 
            "http://mbnavicodes.com", 
            "http://mbnavicodes.com:80", 
            "https://cluster.mb-link.com",
            "https://oldportal.mb-link.com:81",
            "http://oldportal.mb-link.com:81"
        ];
        var origin = req.headers.origin;
        if(allowedOrigins.indexOf(origin) > -1){
            res.header('Access-Control-Allow-Origin', origin);
        }
        res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization");
        res.header("Access-Control-Allow-Credentials", "true");
        res.header("Access-Control-Allow-Methods", "GET,HEAD,OPTIONS,POST,PUT");
        next();
    });    

    /*
    // catch 404 and forward to error handler
    wwwServer.use(function(req, res, next) {
        next(createError(404));
    });

    // error handler
    wwwServer.use(function(err, req, res, next) {
        // set locals, only providing error in development
        res.locals.message = err.message;
        res.locals.error = req.app.get('env') === 'development' ? err : {};
    
        // render the error page
        res.status(err.status || 500);
        res.render('error');
    });
    */
        
    // Create Web Server
    http.createServer(wwwServer).listen(process.env.WEBSERVER_PORT, function(){
        console.log('*********************************');
        console.log('* MbLink Service 2022-2024');
        console.log('* Cluster service listening on');
        console.log('* '+process.env.WEBSERVER_HOST+':'+process.env.WEBSERVER_PORT);
        console.log('*********************************');        
    });  

}

function initWebService() {
    // Init Web Service Modules
    wwwServer.get("/ver",svcAppVersion);
    wwwServer.get("/service",svcService);
    wwwServer.get("/logoff",svcLogoff);
    wwwServer.get("/userinfo",svcUserInfo);
    wwwServer.get("/availabletokens",svcAvailableTokens);
    wwwServer.get("/ping",svcPing);
    wwwServer.get("/tokenhistory",svcTokenHistory);
    wwwServer.get("/creditlist",svcCreditList);    
    wwwServer.get("/tokentransaction",svcTokenTransaction);
    wwwServer.get("/createOnetimetoken",svcCreateOneTimeToken);
    wwwServer.get("/getcreatedtokens",svcGetCreatedTokens);    

    wwwServer.post("/login",svcLogin);
    wwwServer.post("/onetimelogin",svcOneTimeLogin);

    wwwServer.post("/seedkey",svcSeedKeyCalc);
    wwwServer.post("/proc",svcProc);
    wwwServer.post("/gt",svcGetToken);
    wwwServer.post("/register",svcRegister);
    wwwServer.post("/changepassword",svcChangePassword);
    wwwServer.post("/createpayment",svcCreatePayment);
    wwwServer.post("/executepayment",svcExecutePayment);
    wwwServer.post("/generatemapcode",svcGenerateMapCode);
    wwwServer.post("/generatetheftcode",svcGenerateTheftCode);
    wwwServer.post("/vindecoder",svcVINDecoder);
    wwwServer.post("/mapcodes",svcMapCodes);
    wwwServer.post("/sgkt",svcSgkt);
    wwwServer.post("/ecuflashing",svcEcuFlashing);
    wwwServer.post("/killtoken",svcKillToken);
    wwwServer.post("/checkecudiag",svcCheckEcuDiag);
    wwwServer.post("/ecuvariants",svcEcuVariants);    
    wwwServer.post("/ecubackupvariants",svcEcuBackupVariants);    
    wwwServer.post("/ecubackupcheck",svcEcuBackupCheck);    
    wwwServer.post("/ecubackupget",svcEcuBackupGet);    
    wwwServer.post("/ecuerrorcheck",svcEcuErrorCheck);    

    // Mblink Adaptor
    wwwServer.get("/adaptorver",svcAdaptorAppVersion);
    
    // Online SCN
    wwwServer.post("/online/scn",svcOnlineSCN);
    wwwServer.post("/online/ecus",svcOnlineEcus);
}

//
//
// All Logic Starts from Here
//
//
function svcVINDecoder(req,res) {
    res.type('application/json');    
    let response = errorSession();    
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let postData = req.body;
    let userid = req.session["userinfo"]["id"];
    
    if (typeof(postData)==="undefined" || postData === null || postData.vin === null || postData.vin.length != 17 || postData.sku!="vindecoder") {
        res.status(200).send(response);          
        return;
    }    
    
    // Check Credits    
    mbservices.mbVINDecoder.tokenCheck(userid,postData.vin,postData.sku,(result)=>{        
        let response;
        switch (result) {
            case 0: 
                // Gen VIN Code
                mbservices.mbVINDecoder.vinDecode(postData.vin,(resultCode)=>{ 
                    //console.log(resultCode);

                    if (resultCode["state"]==1) { // Added for Generation Process
                        response = vinDecoderResult(300,"Added for Process",{});
                        res.status(200).send(response);
                    } else
                    if (resultCode["state"]==2) { // Wait for Generation Process
                        response = vinDecoderResult(301,"Wait for Process",{});
                        res.status(200).send(response);
                    } else
                    if (resultCode["state"]==3) { // Theft Code found
                        // Spent Credit
                        mbservices.mbVINDecoder.tokenAdd(userid,postData.vin,postData.sku,result["vin"],2,(result)=>{
                            if (result==0) {
                                response = vinDecoderResult(200,"Generated",resultCode);
                                res.status(200).send(response);
                            } else if (result==2) {
                                mbservices.mbVINDecoder.vinDecode(postData.vin,(resultCode)=>{
                                    response = vinDecoderResult(200,"Generated",resultCode);
                                    res.status(200).send(response);
                                });
                            } else {
                                response = vinDecoderResult(401,"Generation Error",0);
                                res.status(200).send(response);
                            }
                        });

                    } else 
                    if (resultCode["state"]==4) { // Login Error to Vedoc
                        response = vinDecoderResult(402,"Incorrect VIN",0);
                        res.status(200).send(response);
                    }
                });
                break;
            case 1: 
                response = vinDecoderResult(400,"Not enough credit",result);
                res.status(200).send(response);
                break;
            case 2:                 
                mbservices.mbVINDecoder.vinDecode(postData.vin,(resultCode)=>{
                    response = vinDecoderResult(200,"Generated",resultCode);
                    res.status(200).send(response);
                });
                break;
        }

    });

}

function svcGenerateTheftCode(req,res) {
    res.type('application/json');    
    let response = errorSession();    
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let postData = req.body;
    let userid = req.session["userinfo"]["id"];
    
    if (typeof(postData)==="undefined" || postData === null || postData.serial === null || postData.serial.length != 14 || postData.sku!="antitheftcode") {
        res.status(200).send(response);          
        return;
    }    
    
    // Check Credits    
    mbservices.mbAntiTheftCode.tokenCheck(userid,postData.serial,postData.sku,(result)=>{        
        let response;
        console.log(result);
        switch (result) {
            case 0: 
                // Gen Theft Code
                mbservices.mbAntiTheftCode.generateTheftCode(postData.serial,(resultCode)=>{ 
                    console.log(resultCode);

                    if (resultCode["state"]==1) { // Added for Generation Process
                        response = antiTheftResult(300,"Added for Process",{});
                        res.status(200).send(response);
                    } else
                    if (resultCode["state"]==2) { // Wait for Generation Process
                        response = antiTheftResult(301,"Wait for Process",{});
                        res.status(200).send(response);
                    } else
                    if (resultCode["state"]==3) { // Theft Code found
                        // Spent Credit
                        if (resultCode["found"]!=3) {
                            mbservices.mbAntiTheftCode.tokenAdd(userid,postData.serial,postData.sku,result["antiTheft"],3,(result)=>{
                                //console.log(result);
                                if (result==0 || result==2) {
                                    response = antiTheftResult(200,"Generated",resultCode);
                                } else {
                                    response = antiTheftResult(401,"Generation Error",0);
                                }
                            });    
                        } else {
                            response = antiTheftResult(200,"Generated",resultCode);
                        }
                        res.status(200).send(response);
                    } else 
                    if (resultCode["state"]==4) { // Login Error to Vedoc
                        response = antiTheftResult(402,"Generation Error",0);
                        res.status(200).send(response);
                    }
                });
                break;
            case 1: 
                response = antiTheftResult(400,"Not enough credit",result);
                res.status(200).send(response);
                break;
            case 2:                 
                mbservices.mbAntiTheftCode.generateTheftCode(postData.serial,(resultCode)=>{
                    response = antiTheftResult(200,"Generated",resultCode);
                    res.status(200).send(response);
                });
                break;
        }

    });

}

function svcEcuBackupVariants(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let userid = req.session["userinfo"]["id"];    
    let postData = req.body;
    if (typeof(postData)==="undefined" || postData === null || postData.ecu === null) {
        res.status(200).send(response);          
        return;
    }

    //console.log(postData.variants);

    mbservices.mbVariantBackup.backup(userid, postData.ecu, postData.cid, postData.serial, postData.hwid, postData.swid, postData.diagname, postData.variants,(result)=>{
        console.log("ECU Variants is backuped",result,postData.serial);
        res.status(200).send(clusterProc(result));
    });
}

function svcEcuVariants(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let userid = req.session["userinfo"]["id"];    
    let postData = req.body;
    if (typeof(postData)==="undefined" || postData === null || postData.ecu === null) {
        res.status(200).send(response);          
        return;
    }

    mbservices.mbVariantBackup.variants(postData.ecu,(result)=>{
        console.log("ECU Variants is requested",postData.ecu);
        res.status(200).send(clusterProc(JSON.stringify(result)));
    });
}

function svcEcuBackupGet(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let userid = req.session["userinfo"]["id"];    
    let postData = req.body;
    if (typeof(postData)==="undefined" || postData === null || postData.ecu === null || postData.serial === null) {
        res.status(200).send(response);          
        return;
    }

    mbservices.mbVariantBackup.get(userid, postData.ecu, postData.serial,(result)=>{
        console.log("ECU Variants is requested to get backup",postData.ecu, postData.serial);
        res.status(200).send(clusterProc(JSON.stringify(result)));
    });
}

function svcEcuErrorCheck(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let userid = req.session["userinfo"]["id"];    
    let postData = req.body;
    if (typeof(postData)==="undefined" || postData === null || postData.ecu === null || postData.codes === null) {
        res.status(200).send(response);          
        return;
    }

    mbservices.mbVariantBackup.error(userid, postData.ecu, postData.codes,(result)=>{
        console.log("ECU Error is requested to check",postData.ecu,postData.codes);
        res.status(200).send(clusterProc(JSON.stringify(result)));
    });
}

function svcEcuBackupCheck(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let userid = req.session["userinfo"]["id"];    
    let postData = req.body;
    if (typeof(postData)==="undefined" || postData === null || postData.ecu === null || postData.serial === null) {
        res.status(200).send(response);          
        return;
    }

    mbservices.mbVariantBackup.check(userid, postData.ecu, postData.serial,(result)=>{
        console.log("ECU Variants is requested to check backup",postData.ecu,postData.serial);
        res.status(200).send(clusterProc(JSON.stringify(result)));
    });
}

function svcAdaptorAppVersion(req,res) {
    res.type('application/json');    
    res.status(200).send({
        version:"1.00",
        firmware:"1.1.8",
        download:"http://download.mb-link.com/adaptor/SWR_CK128.118.production.bin",
        stm: {
            firmware:"1.1.0",
            download:"http://download.mb-link.com/adaptor/STM_1.1.0.production.bin",
            pubsign: "992cb3f05e9baff6f15b35e28a203f9bf3503d0884bf2648ad5022d62ba9fe1dbbb38cb2d697354320cf5f49d4911abe24bf8ddbcc688c58b4017b00d4e3213b9fd09abe117b7fc90dee67b91cc5293e9f3f8575ce0d1b0a29bd5087bd798c6dbe809daa5c0ba7ebcb11fe43c1d89985923031c406ef6f4e70d563091c50da2d8c42baa7a793674172298cf190f7c38344a15d1f94be1a8f82a35153aedff930bcc5920abb16b342d94cfebfe0a6d173fd52a23c1804a8a19667504f1d6fa04a56c38e2450f75fb89a6c12056f753f557de0fc99f57beb82f1863e11cfa7e35e9b2c71c5a393f9e032b1477a07aeba9f1c88697b3d8836b600765ce8992e2268"
        }
    });                 
    res.end();
}

function svcCheckEcuDiag(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let userid = req.session["userinfo"]["id"];
    
    let postData = req.body;
    if (typeof(postData)==="undefined" || postData === null || postData.ecu === null || postData.diag === null ||
        postData.ecu == "" || postData.diag == ""
        ) {
        res.status(200).send(response);          
        return;
    }

    let SQLquery = { query: "CALL checkDiagVersion(?,?);" , params: [ postData.ecu, postData.diag ] };        
    db.QueryExec(SQLquery, (result)=>{   
        
        console.log(postData);

        if (result && result[0].length>0) {
            console.log(result);
            response = { 
                "payload": mbservices.ecuFlashing.encrypt(JSON.stringify(
                    {
                        "version": result[0][0]["caption"]
                    }
                )),
                "code" : 200
            }  

            
        } else {
            response = { 
                "payload" : mbservices.ecuFlashing.encrypt(JSON.stringify({})),
                "code" : 400
            }  
        }
        
        res.status(200).send(response);        
    });

}

function svcEcuFlashing(req,res){
    res.type('application/json');    
    let response = errorSession();
    let postData = req.body;
    
    response = {};
    
    if (typeof(postData)==="undefined" || postData.proc === null) {
        res.status(200).send(response);          
        return;
    }  

    if (postData.name === null && postData.variant === null) {
        res.status(200).send(response);          
        return;
    }

    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    mbservices.ecuFlashing.getMagicNumber(postData.name,postData.variant, function(result){                
        if(!result) {
            response = errorSession();
            res.status(200).send(response);          
        } else {
            response = { 
                "code" : 200, 
                "payload" : result                     
            }    

            console.log("ECU Flashing:",postData);

            res.status(200).send(response);              
        }        
    });        

}

function svcSgkt(req,res){
    res.type('application/json');    
    let response = errorSession();
    let postData = req.body;
    
    response = {};
    
    if (typeof(postData)==="undefined" || postData.proc === null) {
        res.status(200).send(response);          
        return;
    }  

    if (postData.name === null && postData.level === null && postData.seed === null) {
        res.status(200).send(response);          
        return;
    }

    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    mbservices.seedkey.calcseedKey(postData.name+".jar","com.daimler.security.generated."+postData.construct,postData.level,postData.seed, function(result){                
        if(!result) {
            response = errorSession();
            res.status(200).send(response);          
        } else {
            response = { 
                "code" : 200, 
                "payload" : result                     
            }    

            console.log("ECU SGKT:",postData);

            let userid = req.session["userinfo"]["id"];
            let SQLquery = { query: "CALL sgktAdd(?,?,?,?,?);" , params: [ userid, postData.cid,postData.name,postData.level,postData.seed ] };
            db.QueryExec(SQLquery, (result)=>{  
                res.status(200).send(response);          
            });    
    
        }        
    });        

}

function svcMapCodes(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let postData = req.body;
    if (typeof(postData)==="undefined" || postData === null || postData.hu === null) {
        res.status(200).send(response);          
        return;
    }    

    let userid = req.session["userinfo"]["id"];
        mbservices.mbMapGenerator.getMaps(postData.hu,(result)=>{
        response = mapResult(200,"Map Code",result);
        res.status(200).send(response);
    });
    
}

function svcGenerateMapCode(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let postData = req.body;
    let userid = req.session["userinfo"]["id"];

    if (typeof(postData)==="undefined" || postData === null || postData.hu === null || postData.vin === null || postData.id === null || postData.sku === null || postData.vin.length !== 17) {
        res.status(200).send(response);          
        return;
    }    

    if (postData.sku!="astonntg50map" && 
        postData.sku!="ntg35map" &&
        postData.sku!="ntg45map" && 
        postData.sku!="ntg50map" && 
        postData.sku!="ntg51map" && 
        postData.sku!="ntg55map" && 
        postData.sku!="ntg60map") {
            res.status(200).send(response);          
            return;
    }

    // Check Credits
    let cID = postData.vin;
    mbservices.mbMapGenerator.tokenCheck(userid,cID,postData.sku,postData.id,postData.vin,(result)=>{        
        let response;

        switch (result) {
            case 0: 
                // Spent Credit
                mbservices.mbMapGenerator.tokenAdd(userid,cID,postData.sku,postData.id,postData.vin,1,(result)=>{
                    if (result==0) {
                        mbservices.mbMapGenerator.generateMapCode(userid,postData.hu,postData.id,postData.vin,(resultCode)=>{

                            if (resultCode["state"]==1) { // Added for Generation Process
                                response = mapResult(300,"Added for Process",{});
                                res.status(200).send(response);
                            } else
                            if (resultCode["state"]==2) { // Wait for Generation Process
                                response = mapResult(301,"Wait for Process",{});
                                res.status(200).send(response);
                            } else
                            if (resultCode["state"]==3) { // Map Code found
                                // Spent Credit
                                if (resultCode["mapcode"]!=null && resultCode["mapcode"].length==6) {
                                    mbservices.mbClusterManager.tokenConfirm(userid,cID,postData.sku,postData.id,postData.vin,3,(result)=>{                                
                                        response = mapResult(200,"Generated",resultCode);
                                        res.status(200).send(response);
                                    });    
                                } else {
                                    response = mapResult(301,"Wait for Process",{});
                                    res.status(200).send(response);
                                }
                            }     
                            // End of Generation 
                        });
 
                    } else {
                        response = mapResult(401,"Generation Error",0);
                        res.status(200).send(response);        
                    }
                });
                break;
            case 1: 
                response = mapResult(400,"Not enough credit",result);
                res.status(200).send(response);
                break;
            case 2: 
                    mbservices.mbMapGenerator.generateMapCode(userid,postData.hu,postData.id,postData.vin,(resultCode)=>{

                    if (resultCode["state"]==1) { // Added for Generation Process
                        response = mapResult(300,"Added for Process",{});
                        res.status(200).send(response);
                    } else
                    if (resultCode["state"]==2) { // Wait for Generation Process
                        response = mapResult(301,"Wait for Process",{});
                        res.status(200).send(response);
                    } else
                    if (resultCode["state"]==3) { // Map Code Generated
                        // Spent Credit
                        //console.log(resultCode);
                        if (resultCode["mapcode"]!=null && resultCode["mapcode"].length==6) {
                            mbservices.mbClusterManager.tokenConfirm(userid,cID,postData.sku,postData.id,postData.vin,3,(result)=>{                                
                                response = mapResult(200,"Generated",resultCode);
                                res.status(200).send(response);
                            });    
                        } else {
                            response = mapResult(301,"Wait for Process",{});
                            res.status(200).send(response);
                        }
                    }     
                });    
                break;            
        }

    });

}

function svcCreatePayment(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }
    
    let postData = req.body;
    if (typeof(postData)==="undefined" || postData === null || postData.sku === null || postData.sku == "") {
        res.status(200).send(response);          
        return;
    }    

    mbservices.paypal.createPayment(postData.sku, (code,payload)=>{
        response = { 
            "code" : code,
            "payload": payload            
        }  
        res.status(200).send(response);
    });

}

function svcExecutePayment(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let userid = req.session["userinfo"]["id"];    
    let postData = req.body;
    if (typeof(postData)==="undefined" || postData === null || postData.orderid === null || postData.sku === null ||
        postData.orderid == "" || postData.sku == ""
        ) {
        res.status(200).send(response);          
        return;
    }

    mbservices.paypal.executePayment(userid,postData.sku,postData.orderid,postData.authorizationid,(code,payload)=>{
        response = { 
            "code" : code,
            "payload": payload            
        }  
        console.log(response);
        res.status(200).send(response);
    });

}

function svcOrderApprove(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let userid = req.session["userinfo"]["id"];
    
    let postData = req.body;
    if (typeof(postData)==="undefined" || postData === null || postData.orderid === null || postData.sku === null ||
        postData.orderid == "" || postData.sku == ""
        ) {
        res.status(200).send(response);          
        return;
    }

    let SQLquery = { query: "Set @ReturnValue=0; CALL tokenBuy(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, postData.orderid, postData.sku ] };        
    db.QueryExec(SQLquery, (result)=>{  
        console.log (result[2][0]["ReturnValue"]);
        if (result[2][0]["ReturnValue"]==1) {
            response = { 
                "code" : 200
            }  
        } else {
            response = { 
                "code" : 400
            }  
        }
        res.status(200).send(response);        
    });

}

function svcChangePassword(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let userid = req.session["userinfo"]["id"];
    
    let postData = req.body;
    if (typeof(postData)==="undefined" || postData === null || postData.newpassword === null || postData.password === null ||
        postData.newpassword == "" || postData.password == ""
        ) {
        res.status(200).send(response);          
        return;
    }

    let SQLquery = { query: "Set @ReturnValue=0; CALL changeUserPassword(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, postData.password, postData.newpassword ] };        
    db.QueryExec(SQLquery, (result)=>{  
        console.log (result[2][0]["ReturnValue"]);
        if (result[2][0]["ReturnValue"]==1) {
            response = { 
                "code" : 200
            }  
        } else {
            response = { 
                "code" : 400
            }  
        }
        res.status(200).send(response);        
    });

}

function svcRegister(req,res) {
    res.type('application/json');    
    let response = errorRegister();
    
    let postData = req.body;
    if (typeof(postData)==="undefined" || postData === null || postData.name === null || postData.lastname === null || postData.email === null || postData.password === null ||
        postData.name == "" || postData.lastname == "" || postData.email == "" || postData.password == ""
        ) {
        res.status(200).send(response);          
        return;
    }

    let SQLquery = { query: "Set @ReturnValue=0; CALL registerUser(?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ postData.name, postData.lastname, postData.email, postData.password ] };        
    db.QueryExec(SQLquery, (result)=>{  
        console.log (result[2][0]["ReturnValue"]);
        if (result[2][0]["ReturnValue"]==1) {
            response = { 
                "code" : 200
            }  
        }
        res.status(200).send(response);        
    });

}

function svcTokenHistory(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let userid = req.session["userinfo"]["id"];

    let SQLquery = { query: "CALL tokenHistory(?);" , params: [ userid ] };
    db.QueryExec(SQLquery, (result)=>{  
        response = { 
            "code" : 200, 
            "payload" : {
                "history": result[0]
            }    
        }  
        res.status(200).send(response);        
    });

}

function svcTokenTransaction(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let userid = req.session["userinfo"]["id"];

    let SQLquery = { query: "CALL tokenTransaction(?);" , params: [ userid ] };
    db.QueryExec(SQLquery, (result)=>{  
        response = { 
            "code" : 200, 
            "payload" : {
                "history": result[0]
            }    
        }  
        res.status(200).send(response);        
    });

}

function svcCreateOneTimeToken(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let userid = req.session["userinfo"]["id"];

    let SQLquery = { query: "CALL createOneTimeToken(?);" , params: [ userid ] };
    db.QueryExec(SQLquery, (result)=>{  
        response = { 
            "code" : 200, 
            "payload" : {
                "token": result[0]
            }    
        }  
        res.status(200).send(response);        
    });

}

function svcCreditList(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let userid = req.session["userinfo"]["id"];

    let SQLquery = { query: "CALL tokenTypeList();" , params: [ ] };
    db.QueryExec(SQLquery, (result)=>{  
        response = { 
            "code" : 200, 
            "payload" : {
                "credits": result[0]
            }    
        }  
        res.status(200).send(response);        
    });

}

function svcLogoff(req,res) {
    res.type('application/json');    
    destroySession(req);    
    res.status(200).send({code:200});                 
    res.end();
}

function svcAppVersion(req,res) {
    res.type('application/json');    
    res.status(200).send({version:"2.51"});                 
    res.end();
}

function svcAvailableTokens(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let userid = req.session["userinfo"]["id"];
    response = msgLogged(req.session["userinfo"]);

    let SQLquery = { query: "CALL userTokenList(?);" , params: [ userid ] };
    db.QueryExec(SQLquery, (result)=>{  
        let token           = 0;
        let tokenspent      = 0;
        let remainingmapcodes   = 0;
        let lasttokenbuy    = "No date";
        let lasttokenspent  = "No date";
        let memstartdate    = "No date";
        let memexpiredate   = "No date";

        if (result[0].length>0) {
            token           = result[0][0]["tokenamount"];
            tokenspent      = result[0][0]["tokenspent"];
            remainingmapcodes  = result[0][0]["remainingmapcodes"];
            lasttokenbuy    = result[0][0]["lasttokenadded"];
            lasttokenspent  = result[0][0]["tokenspenddate"];
            memstartdate    = result[0][0]["memstartdate"];
            memexpiredate   = result[0][0]["memexpiredate"];
        }
        response = { 
            "code" : 200, 
            "payload" : {
                "token": token,
                "tokenspent": tokenspent,
                "remainingmapcodes": remainingmapcodes==null?0:remainingmapcodes,
                "lasttokenbuy": lasttokenbuy,
                "lasttokenspent": lasttokenspent,
                "memstartdate": memstartdate,
                "memexpiredate": memexpiredate
            }    
        }  
        res.status(200).send(response);        
    });

}

function svcUserInfo(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    //Set @ReturnValue=0; CALL test(?,@ReturnValue); Select @ReturnValue as ReturnValue;
    let userid = req.session["userinfo"]["id"];
    response = msgLogged(req.session["userinfo"]);

    let SQLquery = { query: "CALL userTokenList(?); CALL tokenTypeList();" , params: [ userid ] };
    db.QueryExec(SQLquery, (result)=>{  
        let token               = 0;
        let tokenspent          = 0;
        let remainingmapcodes   = 0;
        let lasttokenbuy        = "No date";
        let lasttokenspent      = "No date";
        let memstartdate        = "No date";
        let memexpiredate       = "No date";
        let mapcodelastgen      = "No date";

        if (result[0].length>0) {
            token               = result[0][0]["tokenamount"];
            tokenspent          = result[0][0]["tokenspent"];
            remainingmapcodes   = result[0][0]["remainingmapcodes"];
            lasttokenbuy        = result[0][0]["lasttokenadded"];
            lasttokenspent      = result[0][0]["tokenspenddate"];
            memstartdate        = result[0][0]["memstartdate"];
            memexpiredate       = result[0][0]["memexpiredate"];
            mapcodelastgen      = result[0][0]["mapcodelastgen"];
        }
        response = { 
            "code" : 200, 
            "payload" : {
                "title": "Session Info", 
                "message": "User successfuly logged",
                "userinfo": req.session["userinfo"],
                "token": token,
                "tokenspent": tokenspent,
                "remainingmapcodes": remainingmapcodes==null?0:remainingmapcodes,
                "lasttokenbuy": lasttokenbuy,
                "lasttokenspent": lasttokenspent,
                "memstartdate": memstartdate,
                "memexpiredate": memexpiredate,
                "mapcodelastgen": mapcodelastgen==null?"No Date":mapcodelastgen,
                "availabletokens": result[2]
            }    
        }  
        res.status(200).send(response);        
    });

}

function svcProc(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    let postData = req.body;
    let userid = req.session["userinfo"]["id"];

    if (typeof(postData)==="undefined" || postData.proc === null) {
        res.status(200).send(response);          
        return;
    }    

console.log(postData);

    switch(postData.proc) {
        case "headunitcheck":            
            procHeadUnit(res,postData);
            break;
        case "feature":            
            res.status(200).send(procFeature(postData.sku));
            break;
        case "eprom":            
            res.status(200).send(procEprom(postData.sku));
            break;
        case "amg":            
            res.status(200).send(procAMG(postData));
            break;
        case "renew":            
            res.status(200).send(procRenew(postData));
            break;
        case "tokencheck":
            mbservices.mbClusterManager.tokenCheck(userid,postData.cid,postData.sku,postData.hwid,postData.swid,(result)=>{
                console.log("TokenCheck:",result);
                res.status(200).send(clusterProc(result));
            });
            break;
        case "spend":
            mbservices.mbClusterManager.tokenAdd(userid,postData.cid,postData.sku,postData.hwid,postData.swid,1,(result)=>{
                console.log(result);
                res.status(200).send(clusterProc(result));
            });
            break;
        case "confirm":
            mbservices.mbClusterManager.tokenConfirm(userid,postData.cid,postData.sku,postData.hwid,postData.swid,postData.state,(result)=>{
                console.log(result);
                res.status(200).send(clusterProc(result));
            });
            break;
        case "eis":            
            res.status(200).send(procEIS(postData.sku));
            break;
        case "unlockcluster":            
            res.status(200).send(procUnlockCluster(postData.sku));
            break;
    
    }

}

function procHeadUnit(res,postData) {
    let response = "";
    switch(postData.sku) {
        case "HU5S1":
        case "HU5S1ENTRY":    
        case "HU5ES2":
            response = mbservices.mbHeadUnit.CheckCarplay(postData.sku,postData.hwid,(result)=>{
                console.log(JSON.stringify(result[0]));
                res.status(200).send(clusterProc(JSON.stringify(result[0])));
            });            
        break;
    }
}

function procUnlockCluster(sku) {
    let response = "";
    switch(sku) {
        case "ic213unlock": 
            response = mbservices.mbClusterManager.IC213.Unlock();
            return clusterProc(response);
        break;
        case "ic222unlock": 
            response = mbservices.mbClusterManager.IC222.Unlock();
            return clusterProc(response);
        break;
        case "ic204unlock": 
            response = mbservices.mbClusterManager.IC204.Unlock();
            return clusterProc(response);
        break;
        case "ic172unlock": 
            response = mbservices.mbClusterManager.IC172.Unlock();
            return clusterProc(response);
        break;
        case "ki221unlock": 
            response = mbservices.mbClusterManager.KI221.Unlock();
            return clusterProc(response);
        break;
        case "ic213disableezssync": 
            response = mbservices.mbClusterManager.IC213.DisableEZSSync();
            return clusterProc(response);
        break;
        case "ic177disablefirewall": 
            response = mbservices.mbClusterManager.IC177.DisableFirewall();
            return clusterProc(response);
        break;
        case "ki211unlock": 
            response = mbservices.mbClusterManager.KI211.Unlock();
            return clusterProc(response);
        break;
        case "ki164unlock": 
            response = mbservices.mbClusterManager.KI164.Unlock();
            return clusterProc(response);
        break;
        case "ki203unlock": 
            response = mbservices.mbClusterManager.KI203.Unlock();
            return clusterProc(response);
        break;
        case "ic907unlock": 
            response = mbservices.mbClusterManager.IC907.Unlock();
            return clusterProc(response);
        break;

    }
}

function procEIS(sku) {
    let response = "";
    switch(sku) {
        case "ezs213unlock": 
            response = mbservices.mbEISManager.EZS213.DisableFirewall();
            return clusterProc(response);
        break;
        case "ezs167unlock": 
            response = mbservices.mbEISManager.EZS167.DisableFirewall();
            return clusterProc(response);
        break;
        case "eis447unlock": 
            response = mbservices.mbEISManager.EIS447.DisableFirewall();
            return clusterProc(response);
        break;

    }
}

function procFeature(sku) {
    let response = "";
    switch(sku) {
        case "ic204tachodizel": 
            response = mbservices.mbClusterManager.IC204.TachoDizel();
            return clusterProc(response);
        break;
        case "ic204tachoc63": 
            response = mbservices.mbClusterManager.IC204.TachoC63();
            return clusterProc(response);
        break;
        case "ic204tachobenzin": 
            response = mbservices.mbClusterManager.IC204.TachoBenzin();
            return clusterProc(response);
        break;
        case "ic204speedo320km": 
            response = mbservices.mbClusterManager.IC204.Speedo320KM();
            return clusterProc(response);
        break;
        case "ic204speedo260km": 
            response = mbservices.mbClusterManager.IC204.Speedo260KM();
            return clusterProc(response);
        break;
        case "ic204speedo160ml": 
            response = mbservices.mbClusterManager.IC204.Speedo160ML();
            return clusterProc(response);
        break;
        case "ic204speedo340brabus": 
            response = mbservices.mbClusterManager.IC204.Speedo340Brabus();
            return clusterProc(response);
        break;

    }
}

function procAMG(postData) {
    let response = "";
    switch(postData.sku) {
        case "ic213amganalog": 
            response = mbservices.mbClusterManager.IC213.AMGAnalog();
            return clusterProc(response);
        break;
        case "ic213amg": 
            response = mbservices.mbClusterManager.IC213.AMG280KM();
            return clusterProc(response);
        break;
        case "ic213km": 
            response = mbservices.mbClusterManager.IC213.AMG330KM();
            return clusterProc(response);
        break;
        case "ic213yellow": 
            response = mbservices.mbClusterManager.IC213.AMGYellow();
            return clusterProc(response);
        break;
        case "ic213noyellow": 
            response = mbservices.mbClusterManager.IC213.AMGNoYellow();
            return clusterProc(response);
        break;
        case "ic213noamg": 
            response = mbservices.mbClusterManager.IC213.AMGDisable();
            return clusterProc(response);
        break;
        case "ic222noamg": 
            response = mbservices.mbClusterManager.IC222.AMGDisable();
            return clusterProc(response);
        break;
        case "ic222amg": 
            response = mbservices.mbClusterManager.IC222.AMGEnable();
            return clusterProc(response);
        break;
        case "ic205amg": 
            response = mbservices.mbClusterManager.IC222.AMGEnable205();
            return clusterProc(response);
        break;
        case "ic222km": 
            response = mbservices.mbClusterManager.IC222.AMG330KM();
            return clusterProc(response);
        break;
        case "ic222km360": 
            response = mbservices.mbClusterManager.IC222.AMG360KM();
            return clusterProc(response);
        break;
        case "ic204amg": 
            response = mbservices.mbClusterManager.IC204.AMGEnable();
            return clusterProc(response);
        break;
        case "ic204amg2012": 
            response = mbservices.mbClusterManager.IC204.AMGW212W218Enable();
            return clusterProc(response);
        break;
        case "ic204amg218": 
            response = mbservices.mbClusterManager.IC204.AMGW218Enable();
            return clusterProc(response);
        break;
        case "ic204amg5potdiesel": 
            response = mbservices.mbClusterManager.IC204.AMGEnable5PotDiesel();
            return clusterProc(response);
        break;
        case "ic204amgmono": 
            response = mbservices.mbClusterManager.IC204.AMGWMonoEnable();
            return clusterProc(response);
        break;
        case "ic204noamg": 
            response = mbservices.mbClusterManager.IC204.AMGDisable();
            return clusterProc(response);
        break;
        case "ic172amg": 
            response = mbservices.mbClusterManager.IC172.AMGEnable();
            return clusterProc(response);
        break;
        case "ic172amgesp": 
            response = mbservices.mbClusterManager.IC172.AMGEnableESP();
            return clusterProc(response);
        break;
        case "ic172amg1": 
            response = mbservices.mbClusterManager.IC172.AMGEnable1();
            return clusterProc(response);
        break;
        case "ic172amg2": 
            response = mbservices.mbClusterManager.IC172.AMGEnable2();
            return clusterProc(response);
        break;
        case "ic172amg3": 
            response = mbservices.mbClusterManager.IC172.AMGEnable3();
            return clusterProc(response);
        break;
        case "ic172amg4": 
            response = mbservices.mbClusterManager.IC172.AMGEnable4();
            return clusterProc(response);
        break;
        case "ic172amg5": 
            response = mbservices.mbClusterManager.IC172.AMGEnable5();
            return clusterProc(response);
        break;
        case "ic172amg6": 
            response = mbservices.mbClusterManager.IC172.AMGEnable6();
            return clusterProc(response);
        break;
        case "ic172amgmanual": 
            response = mbservices.mbClusterManager.IC172.AMGEnableManual();
            return clusterProc(response);
        break;
        case "ic172noamg": 
            response = mbservices.mbClusterManager.IC172.AMGDisable();
            return clusterProc(response);
        break;
        case "ic172biturbo": 
            response = mbservices.mbClusterManager.IC172.EnableBiTurbo();
            return clusterProc(response);
        break;
        case "ic172nobiturbo": 
            response = mbservices.mbClusterManager.IC172.DisableBiTurbo();
            return clusterProc(response);
        break;
        case "ic172brabus": 
            response = mbservices.mbClusterManager.IC172.EnableBrabus();
            return clusterProc(response);
        break;
        case "ic172nobrabus": 
            response = mbservices.mbClusterManager.IC172.DisableBrabus();
            return clusterProc(response);
        break;
        case "ki221brabus320": 
            response = mbservices.mbClusterManager.KI221.BrabusEnable320();
            return clusterProc(response);
        break;
        case "ki221brabus360": 
            response = mbservices.mbClusterManager.KI221.BrabusEnable360();
            return clusterProc(response);
        break;
        case "ki221preamg320": 
            response = mbservices.mbClusterManager.KI221.PreAMGEnable320();
            return clusterProc(response);
        break;
        case "ki221preamg360": 
            response = mbservices.mbClusterManager.KI221.PreAMGEnable360();
            return clusterProc(response);
        break;
        case "ki221flamg320": 
            response = mbservices.mbClusterManager.KI221.FLAMGEnable320();
            return clusterProc(response);
        break;
        case "ki221flamg360": 
            response = mbservices.mbClusterManager.KI221.FLAMGEnable360();
            return clusterProc(response);
        break;
        case "ki221noamg": 
            response = mbservices.mbClusterManager.KI221.AMGDisable();
            return clusterProc(response);
        break;
        case "ki211amg": 
            response = mbservices.mbClusterManager.KI211.AMGEnable(postData.swid.split("|")[0]);
            return clusterProc(response);
        break;
        case "ki211noamg": 
            response = mbservices.mbClusterManager.KI211.AMGDisable(postData.swid.split("|")[0]);
            return clusterProc(response);
        break;
        case "ki164amg": 
            response = mbservices.mbClusterManager.KI164.AMGEnable();
            return clusterProc(response);
        break;
        case "ki164noamg": 
            response = mbservices.mbClusterManager.KI164.AMGDisable();
            return clusterProc(response);
        break;

    }
}

function procRenew(postData) {
    let response = "";
    switch(postData.sku) {
        case "ic213renew": 
            response = mbservices.mbClusterManager.IC213.Virginize();
            return clusterProc(response);
        break;
        case "ic222renew": 
            response = mbservices.mbClusterManager.IC222.VirginMethod3();
            return clusterProc(response);
        break;
        case "ic222virgincheck": 
            response = mbservices.mbClusterManager.IC222.CheckVirginizable();
            return clusterProc(response);
        break;
        case "ic222virgin": 
            response = mbservices.mbClusterManager.IC222.VirginMethod2();
            return clusterProc(response);
        break;
        case "ic204renew": 
            response = mbservices.mbClusterManager.IC204.Virgin(postData.swid.split("|")[0]);
            return clusterProc(response);
        break;
        case "ic204renewmono": 
            response = mbservices.mbClusterManager.IC204.VirginMono();
            return clusterProc(response);
        break;
        case "ic172renew": 
            response = mbservices.mbClusterManager.IC172.Virgin();
            return clusterProc(response);
        break;
        case "ki221renew": 
            response = mbservices.mbClusterManager.KI221.Virgin();
            return clusterProc(response);
        break;
        case "ki221renew2": 
            response = mbservices.mbClusterManager.KI221.Virgin2(postData.swid.split("|")[2]);
            return clusterProc(response);
        break;
        case "ki211renew": 
            response = mbservices.mbClusterManager.KI211.Virgin(postData.swid.split("|")[0]);
            return clusterProc(response);
        break;
        case "ki164renew": 
            response = mbservices.mbClusterManager.KI164.Virgin();
            return clusterProc(response);
        break;
        case "ki203renew": 
            response = mbservices.mbClusterManager.KI203.Virgin();
            return clusterProc(response);
        break;
        case "ic907renew": 
            response = mbservices.mbClusterManager.IC907.Virginize();            
            return clusterProc(response);
        break;
        case "ic907renew2": 
            response = mbservices.mbClusterManager.IC907.Virginize2();
            return clusterProc(response);
        break;

    }
}

function procEprom(sku) {
    let response = "";
    switch(sku) {
        case "ic213edit": 
            response = mbservices.mbClusterManager.IC213.EpromEdit();
            return clusterProc(response);
        break;
        case "ic222edit": 
            response = mbservices.mbClusterManager.IC222.EpromEdit();
            return clusterProc(response);
        break;
        case "ic204edit": 
            response = mbservices.mbClusterManager.IC204.EpromEdit();
            return clusterProc(response);
        break;
        case "ic172edit": 
            response = mbservices.mbClusterManager.IC172.EpromEdit();
            return clusterProc(response);
        break;
        case "ki221edit": 
            response = mbservices.mbClusterManager.KI221.EpromEdit();
            return clusterProc(response);
        break;
        case "ki211edit": 
            response = mbservices.mbClusterManager.KI211.EpromEdit();
            return clusterProc(response);
        break;
        case "ki164edit": 
            response = mbservices.mbClusterManager.KI164.EpromEdit();
            return clusterProc(response);
        break;
        case "ki203edit": 
            response = mbservices.mbClusterManager.KI203.EpromEdit();
            return clusterProc(response);
        break;
        case "ic907edit": 
            response = mbservices.mbClusterManager.IC907.EpromEdit();
            return clusterProc(response);
        break;
    }
}

function svcGetToken(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }
    let postData = req.body;

    if (typeof(postData)==="undefined" || postData.proc === null) {
        res.status(200).send(response);          
        return;
    }    

    response = { 
        "code" : 200, 
        "payload" : {
            "result": mbservices.mbClusterManager.createTokenId(postData.hwid,postData.swid)
        }    
    } 
    res.status(200).send(response);
}

function svcPing(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    response = { 
        "code" : 200, 
        "payload" : {
            "title": "Session Info", 
            "message": "User successfuly logged",
            "ping": "pong"
        }    
    } 
    res.status(200).send(response);
}

function svcLogin(req,res) {
    res.type('application/json');    
    let response = errorAlreadyLogged();
    let postData = req.body;
    var userIp = req.headers['x-forwarded-for'] || req.socket.remoteAddress || null;    

    console.log(req["headers"]);

    if (checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    if (typeof(postData)==="undefined" || postData.proc === null) {
        res.status(200).send(response);          
        return;
    }  

    let SQLquery = { query: "CALL checkUser(?,?)" , params: [postData.username, postData.password] };
    db.QueryExec(SQLquery, (result)=>{  
        console.log(result);            

        if(result === null || result===false || result[0].length!=1) {
            response = errorSession();
            destroySession(req);
            res.status(200).send(response);                 
            return;
        } 

        req.session["userinfo"] = result[0][0];
        req.session["logged"] = true; 
        response = msgLogged(result[0][0]);

        let userid = req.session["userinfo"]["id"];
        response = msgLogged(req.session["userinfo"]);

        // Store Logins
        db.QueryExec({ query: "CALL storeLogins(?,?,?,?,?)" , params: [userid,req["headers"]["machine-id"]||"Web", req["headers"]["hdd-id"]||"Web",req["headers"]["user-agent"],userIp] }, (result)=>{

            console.log("User Info:",userid,req["headers"]["machine-id"]||"Web", req["headers"]["hdd-id"]||"Web",req["headers"]["user-agent"],userIp);

            let SQLquery = { query: "CALL userTokenList(?); CALL tokenTypeList();" , params: [ userid ] };
            db.QueryExec(SQLquery, (result)=>{  
                let token           = 0;
                let tokenspent      = 0;
                let remainingmapcodes   = 0;
                let lasttokenbuy    = "No date";
                let lasttokenspent  = "No date";

                if (result[0].length>0) {
                    token           = result[0][0]["tokenamount"];
                    tokenspent      = result[0][0]["tokenspent"];
                    remainingmapcodes   = result[0][0]["remainingmapcodes"];
                    lasttokenbuy    = result[0][0]["lasttokenadded"];
                    lasttokenspent  = result[0][0]["tokenspenddate"];
                }
                response = { 
                    "code" : 200, 
                    "payload" : {
                        "title": "Session Info", 
                        "message": "User successfuly logged",
                        "userinfo": req.session["userinfo"],
                        "token": token,
                        "tokenspent": tokenspent,
                        "remainingmapcodes": remainingmapcodes==null?0:remainingmapcodes,
                        "lasttokenbuy": lasttokenbuy,
                        "lasttokenspent": lasttokenspent,
                        "availabletokens": result[2]
                    }   
                } 
                res.status(200).send(response);        
                res.end();
            });     //  Token List

        }); // Store User Info
    
    });        
}

function svcGetCreatedTokens(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }
    let postData = req.body;

    if (typeof(postData)==="undefined" || postData.token === null) {
        res.status(200).send(response);          
        return;
    }    

    response = { 
        "code" : 200, 
        "payload" : {
            "title": "Token Info", 
            "tokens": []
            }    
    }     

    let activeTokens = [];
    for (var element in sessionStore.sessions) {
        let sessionList = JSON.parse(sessionStore.sessions[element]);
        if (sessionList.hasOwnProperty("onetimetoken") && sessionList["userinfo"]["id"]===req.session["userinfo"]["id"]) {  
            activeTokens.push(sessionList["onetimetoken"]);
        }
    }

    let SQLquery = { query: "CALL getActiveTokens(?);" , params: [ req.session["userinfo"]["id"] ] };
    db.QueryExec(SQLquery, (result)=>{  

        result[0].forEach(createdTokens=> {
            createdTokens["active"] = 0;
            activeTokens.forEach(token => {            
                if (token===createdTokens["token"]) {
                    createdTokens["active"] = 1;
                    console.log(createdTokens);
                }    
            });
            
            response["payload"]["tokens"].push(createdTokens);    
        });

        res.status(200).send(response);        
        res.end();      
    
    });

}

function svcKillToken(req,res) {
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }
    let postData = req.body;

    if (typeof(postData)==="undefined" || postData.token === null) {
        res.status(200).send(response);          
        return;
    }    

    response = { 
        "code" : 204, 
        "payload" : {
            "title": "Token Info", 
            "message": "Token not found!"                
            }    
    }     
    
    for (var element in sessionStore.sessions) {
        let sessionList = JSON.parse(sessionStore.sessions[element]);

        if (sessionList.hasOwnProperty("onetimetoken") && sessionList["onetimetoken"]===postData.token && sessionList["userinfo"]["id"]===req.session["userinfo"]["id"]) {            
           delete sessionStore.sessions[element];           
           response = { 
            "code" : 200, 
            "payload" : {
                "title": "Token Info", 
                "message": "Token is killed"                
                }    
            }             
        }
    }
    console.log(sessionStore.sessions);
    
    res.status(200).send(response);        
    res.end();      
}

function svcOneTimeLogin(req,res) {
    res.type('application/json');    
    let response = errorAlreadyLogged();
    let postData = req.body;
    var userIp = req.headers['x-forwarded-for'] || req.socket.remoteAddress || null;    

    if (checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    if (typeof(postData)==="undefined" || postData.proc === null) {
        res.status(200).send(response);          
        return;
    }  

    let SQLquery = { query: "CALL checkOneTimeLogin(?)" , params: [postData.token] };
    db.QueryExec(SQLquery, (result)=>{  
        console.log(result);

        if(result === null || result===false || result[0].length!=1) {
            response = errorSession();
            destroySession(req);
            res.status(200).send(response);                 
            return;
        } 

        req.session["userinfo"] = result[0][0];
        req.session["logged"] = true; 
        req.session["onetimetoken"] = postData.token;
            
        response = msgLogged(result[0][0]);

        let userid = req.session["userinfo"]["id"];
        response = msgLogged(req.session["userinfo"]);

        // Store Logins
        db.QueryExec({ query: "CALL storeLogins(?,?,?,?,?)" , params: [userid,req["headers"]["machine-id"]||"Web", req["headers"]["hdd-id"]||"Web",req["headers"]["user-agent"],userIp] }, (result)=>{

            let SQLquery = { query: "CALL userTokenList(?); CALL tokenTypeList();" , params: [ userid ] };
            db.QueryExec(SQLquery, (result)=>{  
                let token           = 0;
                let tokenspent      = 0;
                let lasttokenbuy    = "No date";
                let lasttokenspent  = "No date";
                if (result[0].length>0) {
                    token           = result[0][0]["tokenamount"];
                    tokenspent      = result[0][0]["tokenspent"];
                    lasttokenbuy    = result[0][0]["lasttokenadded"];
                    lasttokenspent  = result[0][0]["tokenspenddate"];
                }
                response = { 
                    "code" : 200, 
                    "payload" : {
                        "title": "Session Info", 
                        "message": "User successfuly logged",
                        "userinfo": req.session["userinfo"],
                        "token": token,
                        "tokenspent": tokenspent,
                        "lasttokenbuy": lasttokenbuy,
                        "lasttokenspent": lasttokenspent,
                        "availabletokens": result[2]
                    }   
                } 
                res.status(200).send(response);        
                res.end();
            });    
        });    
    
    });        
}

function svcService(req,res){
    res.type('application/json');    
    let response = errorSession();
    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }
    res.status(200).send(mbservices.getMBServiceStates());
}

function svcSeedKeyCalc(req,res){
    res.type('application/json');    
    let response = errorSession();
    let postData = req.body;
    
    response = {};
    
    if (typeof(postData)==="undefined" || postData.proc === null) {
        res.status(200).send(response);          
        return;
    }  

    if (postData.name === null && postData.level === null && postData.seed === null) {
        res.status(200).send(response);          
        return;
    }

    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    mbservices.seedkey.calcseedKeyIC204(postData.name,postData.level,postData.seed, function(result){                
        if(!result) {
            response = errorSession();
            res.status(200).send(response);          
        } else {
            response = { 
                "code" : 200, 
                "payload" : result                     
            }    
            let userid = req.session["userinfo"]["id"];
            let SQLquery = { query: "CALL sgktAdd(?,?,?,?,?);" , params: [ userid, postData.cid,postData.name,postData.level,postData.seed ] };
            db.QueryExec(SQLquery, (result)=>{  
                res.status(200).send(response);          
            });    
    
        }        
    });        

}

function svcOnlineSCN(req,res){
    res.type('application/json');    
    let response = errorSession();
    let postData = req.body;
    
    response = {};
    
    console.log(postData.seed);
    
    if (typeof(postData)==="undefined" || postData.proc === null) {
        res.status(200).send(response);          
        return;
    }  

    if (postData.vin === null || postData.ecuname === null || postData.usevedoc === null) {
        res.status(200).send(response);          
        return;
    }

    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    const config = {
        useVedoc:   postData.usevedoc || true,
        hwid:       postData.hwid || "",
        process:    "scn",
        diagName:   postData.ecuname
    }

    mbservices.mbServer.getscn(postData.vin,config, function(result){                
        if(!result) {
            response = errorSession();
        } else {            
            response = { 
                "code" : 200, 
                "payload" : result                     
            }    
        }        
        res.status(200).send(response);          
    });        

}

function svcOnlineEcus(req,res){
    res.type('application/json');    
    let response = errorSession();
    let postData = req.body;
    
    response = {};
    
    console.log(postData.seed);

    if (typeof(postData)==="undefined" || postData.proc === null) {
        res.status(200).send(response);          
        return;
    }  

    if (postData.vin === null && postData.ecuname === null) {
        res.status(200).send(response);          
        return;
    }

    if (!checkSession(req)) {
        res.status(200).send(response); 
        return;
    }

    mbservices.mbServer.getecus(postData.vin, function(result){                
        if(!result) {
            response = errorSession();
        } else {            
            response = { 
                "code" : 200, 
                "payload" : result                     
            }    
        }        
        res.status(200).send(response);          
    });        

}

/*
    Custom Functions
*/

function vinDecoderResult(code,title,result) {
    const response = { 
        "code" : code, 
        "payload" : {
            "title": title, 
            "result": result
        }    
    }    
    return response;
}

function antiTheftResult(code,title,result) {
    const response = { 
        "code" : code, 
        "payload" : {
            "title": title, 
            "result": result
        }    
    }    
    return response;
}

function mapResult(code,title,result) {
    const response = { 
        "code" : code, 
        "payload" : {
            "title": title, 
            "result": result
        }    
    }    
    return response;
}
function clusterProc(result) {
    console.log(result);
    result = utils.encrypt(result);
    const response = { 
        "code" : 200, 
        "payload" : {
            "title": "Proc", 
            "message": "User Calls",
            "result": result
        }    
    }    
    return response;
}

function checkSession(req, res, next) {
    //console.log(req.session);
    return (req.session["logged"]!=null && req.session.logged);
}

function destroySession(req) {    
    if (req.session.logged) {
        req.session.logged = false;  
        req.session.userinfo = null;
    } 
    //console.log(req.session);
}

function unixTime() {
    return Math.round((new Date()).getTime() / 1000);    
}

function errorRegister() {
    const response = { 
        "code" : 404, 
        "payload" : {
            "title": "Register Info", 
            "message": "Error on Register"
        }    
    }    
    return response;
}

function errorSession() {
    const response = { 
        "code" : 404, 
        "payload" : {
            "title": "Session Info", 
            "message": "Login Check Credentials"
        }    
    }    
    return response;
}

function msgLogged(userinfo) {
    const response = { 
        "code" : 200, 
        "payload" : {
            "title": "Session Info", 
            "message": "User successfuly logged",
            "userinfo": userinfo
        }    
    }    
    return response;
}

function errorAlreadyLogged() {
    const response = { 
        "code" : 405, 
        "payload" : {
            "title": "Session Info", 
            "message": "Already logged"
        }    
    }    
    return response;
}

function errorDatabase() {
    const response = { 
        "code" : 500, 
        "payload" : {
            "title": "Database Info",
            "message": "Database error occured!"
        }
}    
    return response;
}

function init(){
    try {
        setWebServer();
        initWebService();    
    } catch(e) {
        console.log(e);
    }
}

//
// Init Application
//
init();

