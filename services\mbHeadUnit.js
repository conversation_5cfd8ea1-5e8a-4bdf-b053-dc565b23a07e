"use strict"
/*************************
    MB Services Classes
    Coded by <PERSON><PERSON>
**************************/
import md5 from "md5";

class MBHeadUnit {
    constructor (dbConnector) {
        this.db = dbConnector;
        
        this.createTokenId = this.createTokenId.bind(this);         
    }    
    
    createTokenId(hwid,swid) {        
        return md5(hwid+swid);
    }

    CheckCarplay(sku,hwid,callback) {
        let _this = this;        
        let SQLquery = { query: "CALL headunitCheck(?,?);" , params: [sku, hwid] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            //console.log(result);
            if (result[0].length>0) {
                callback(result[0]);
            }
        });           
    }     
    
}
export default MBHeadUnit;
