"use strict"
/*************************
    MB Services Classes
    Coded by <PERSON><PERSON>
**************************/
import TaskManager from "./taskmanager";
import Seed<PERSON>ey from "./seedKey";
import MBServer from "./mbServer";
import MBClusterManager from "./mbClusterManager";
import MBEISManager from "./mbEISManager";
import MBMapGenerator from "./mbMapGenerator";
import Paypal from "./paypal";
import ECUFlasing from "./ecuFlashing";
import MBVariantBackup from "./mbVariantBackup";
import MBAntiTheftCode from "./mbAntiTheftCode";
import MBVINDecoder from "./mbVINDecoder";
import MBHeadUnit from "./mbHeadUnit";

class MBServices {
    constructor (dbConnector) {
        this.db = dbConnector;
        this.taskManager=TaskManager();

        this.seedkey = new SeedKey(this.db, this.taskManager);        
        this.mbServer = new MBServer(this.db, this.taskManager);        
        this.mbClusterManager = new MBClusterManager(this.db);        
        this.mbEISManager = new MBEISManager(this.db);        
        this.mbMapGenerator = new MBMapGenerator(this.db);        
        this.paypal = new Paypal(this.db);
        this.ecuFlashing = new ECUFlasing(this.db);
        this.mbVariantBackup = new MBVariantBackup(this.db);
        this.mbAntiTheftCode = new MBAntiTheftCode(this.db);
        this.mbVINDecoder = new MBVINDecoder(this.db);
        this.mbHeadUnit = new MBHeadUnit(this.db);

        //this.setGarbageCollector();
    }    

    setGarbageCollector() {
        let taskItem = new this.taskManager.TaskItem("Service Garbage Collector");
        taskItem.setTimeOut(15*1000);
        taskItem.setWorker(() => {
            try {
                // GC a Job
                console.log("GC done...");
                taskItem.setRunning(false);
            } catch(e) {
                taskItem.setRunning(false);
                self.error({
                    "taskid": taskItem.taskId,
                    "error": e
                });
            }
        });

        this.taskManager.createTask(taskItem);
    }

    getMBServiceStates() {
        return this.taskManager.taskList();
    }
}
export default MBServices;
