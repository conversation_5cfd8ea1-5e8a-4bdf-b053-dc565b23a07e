"use strict"
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/
import moment from "moment"
import axios from "axios";
import md5 from "md5";
import MBClusterManagerBase from "../mbClusterManagerBase";
import utils from "../../utils";

class SPC213 extends MBClusterManagerBase {
    constructor (dbConnector) {     
        super(dbConnector);   

    }

    DisableFirewall() {
        let CMDResponse =
        "{" +
        "    \"workflow\":" +
        "        [" +
        "            { \"proc\": \"clear\"}," +
        "            { \"proc\": \"init\"}," +
        "            { \"proc\": \"getswid\"}," +
        "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }" +
        "        ]" +
        "}";     
        
        return CMDResponse;
    }


}
export default SPC213;

