"use strict"
/*************************
    MB Services Classes
    Coded by <PERSON><PERSON>
**************************/
import md5 from "md5";
import IC213 from "./clusters/IC213";
import IC222 from "./clusters/IC222";
import IC204 from "./clusters/IC204";
import IC172 from "./clusters/IC172";
import KI221 from "./clusters/KI221";
import IC177 from "./clusters/IC177";
import KI211 from "./clusters/KI211";
import KI164 from "./clusters/KI164";
import KI203 from "./clusters/KI203";
import IC907 from "./clusters/IC907";

class MBClusterManager {
    constructor (dbConnector) {
        this.db = dbConnector;

        this.IC213 = new IC213(this.db);      
        this.IC222 = new IC222(this.db);      
        this.IC204 = new IC204(this.db);      
        this.IC172 = new IC172(this.db);      
        this.KI221 = new KI221(this.db);      
        this.IC177 = new IC177(this.db);      
        this.KI211 = new KI211(this.db);      
        this.KI164 = new KI164(this.db);   
        this.KI203 = new KI203(this.db);      
        this.IC907 = new IC907(this.db);      
        
        this.createTokenId = this.createTokenId.bind(this); 
        this.tokenAdd = this.tokenAdd.bind(this); 
    }    
    
    createTokenId(hwid,swid) {        
        return md5(hwid+swid);
    }

    tokenCheck(userid,cid,sku,hwid,swid,callback) {
        let _this = this;        
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenCheck(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, md5(cid+sku+hwid+swid), sku ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });    
    }
    
    tokenAdd(userid,cid,sku,hwid,swid,state,callback) {
        let _this = this;
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenAdd(?,?,?,?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, md5(cid+sku+hwid+swid), cid, sku, hwid, swid, state ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });    
    }

    tokenConfirm(userid,cid,sku,hwid,swid,state,callback) {
        let _this = this;
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenConfirm(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, md5(cid+sku+hwid+swid), state ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });   
    }    
    
}
export default MBClusterManager;
