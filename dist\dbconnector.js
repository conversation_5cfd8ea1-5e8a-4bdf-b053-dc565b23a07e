"use strict";
/*************************
    Async DB Connector
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _mysql = require("mysql");

var _mysql2 = _interopRequireDefault(_mysql);

var _async = require("async");

var _async2 = _interopRequireDefault(_async);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var DBConnector = function () {
    function DBConnector() {
        _classCallCheck(this, DBConnector);

        this.pool = _mysql2.default.createPool({
            connectionLimit: 100,
            host: '*************',
            user: 'xentry',
            password: 'Xy2020Xp!',
            database: 'xentry',
            multipleStatements: true,
            debug: false
        });
    }

    _createClass(DBConnector, [{
        key: "QueryExec",
        value: function QueryExec(SQLquery, callback) {
            var self = this;

            _async2.default.waterfall([function (callback) {
                self.pool.getConnection(function (err, connection) {
                    if (err) {
                        callback(true);
                    } else {
                        callback(null, connection);
                    }
                });
            }, function (connection, callback) {
                callback(null, connection, SQLquery);
            }, function (connection, SQLquery, callback) {
                try {
                    connection.query(SQLquery.query, SQLquery.params, function (err, rows) {
                        connection.release();
                        if (!err) {
                            callback((typeof rows.length === 'undefined' || rows.length === 0) && rows.affectedRows === 0 ? false : rows);
                        } else {
                            callback(true);
                        }
                    });
                } catch (e) {
                    callback(false);
                }
            }], function (result) {
                if (typeof result === "boolean" && result === true) {
                    callback(null);
                } else {
                    callback(result);
                }
            });
        }
    }]);

    return DBConnector;
}();

exports.default = DBConnector;