"use strict";
/*************************
    MB Variant Backup
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var MBAntiTheftCode = function () {
    function MBAntiTheftCode(dbConnector) {
        _classCallCheck(this, MBAntiTheftCode);

        this.db = dbConnector;

        this.createTokenId = this.createTokenId.bind(this);
        this.get = this.get.bind(this);
        this.add = this.add.bind(this);
        this.generateTheftCode = this.generateTheftCode.bind(this);
        this.tokenAdd = this.tokenAdd.bind(this);
        this.tokenCheck = this.tokenCheck.bind(this);
        this.tokenConfirm = this.tokenConfirm.bind(this);
    }

    _createClass(MBAntiTheftCode, [{
        key: "createTokenId",
        value: function createTokenId(serial) {
            return serial;
        }
    }, {
        key: "add",
        value: function add(serial, callback) {
            var _this = this;
            var SQLquery = { query: "CALL antitheftcodeAdd(?);", params: [serial] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[0]);
            });
        }
    }, {
        key: "get",
        value: function get(serial, callback) {
            var _this = this;
            var SQLquery = { query: "CALL antitheftcodeGet(?);", params: [serial] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[0]);
            });
        }
    }, {
        key: "generateTheftCode",
        value: function generateTheftCode(serial, callback) {
            var _this2 = this;

            var resultFromGen = {
                vinNumber: "",
                antiTheft: "",
                serial: "",
                history: 0,
                found: 0,
                state: 0,
                error: ""
            };

            this.get(serial, function (dbresult) {
                if (dbresult != null && dbresult.length != 1) {
                    // Added to DB for Generation Code
                    _this2.add(serial, function (dbresult) {
                        resultFromGen.vinNumber = dbresult[0].vin;
                        resultFromGen.serial = dbresult[0].serial;
                        resultFromGen.antiTheft = dbresult[0].theftcode;
                        resultFromGen.history = dbresult[0].history;
                        resultFromGen.found = 0;
                        resultFromGen.state = dbresult[0].state;
                        callback(resultFromGen);
                    });
                } else {
                    // Send Existing Data from DB
                    if (dbresult[0] != null) {
                        resultFromGen.vinNumber = dbresult[0].vin;
                        resultFromGen.serial = dbresult[0].serial;
                        resultFromGen.antiTheft = dbresult[0].theftcode;
                        resultFromGen.history = dbresult[0].history;
                        resultFromGen.found = dbresult[0].found;
                        resultFromGen.state = dbresult[0].state;
                        if (dbresult[0].found == 3) {
                            resultFromGen.error = "Incorrect Serial";
                        }
                    }
                    callback(resultFromGen);
                }
            });
        }
    }, {
        key: "tokenCheck",
        value: function tokenCheck(userid, cid, sku, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenCheck(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, cid, sku] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }, {
        key: "tokenAdd",
        value: function tokenAdd(userid, cid, sku, theftcode, state, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenAdd(?,?,?,?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, cid, cid, sku, cid, theftcode, state] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }, {
        key: "tokenConfirm",
        value: function tokenConfirm(userid, cid, state, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenConfirm(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, cid, state] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }]);

    return MBAntiTheftCode;
}();

exports.default = MBAntiTheftCode;