"use strict";
/*************************
    MB Services Classes
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

var _IC = require("./clusters/IC213");

var _IC2 = _interopRequireDefault(_IC);

var _IC3 = require("./clusters/IC222");

var _IC4 = _interopRequireDefault(_IC3);

var _IC5 = require("./clusters/IC204");

var _IC6 = _interopRequireDefault(_IC5);

var _IC7 = require("./clusters/IC172");

var _IC8 = _interopRequireDefault(_IC7);

var _KI = require("./clusters/KI221");

var _KI2 = _interopRequireDefault(_KI);

var _IC9 = require("./clusters/IC177");

var _IC10 = _interopRequireDefault(_IC9);

var _KI3 = require("./clusters/KI211");

var _KI4 = _interopRequireDefault(_KI3);

var _KI5 = require("./clusters/KI164");

var _KI6 = _interopRequireDefault(_KI5);

var _KI7 = require("./clusters/KI203");

var _KI8 = _interopRequireDefault(_KI7);

var _IC11 = require("./clusters/IC907");

var _IC12 = _interopRequireDefault(_IC11);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var MBClusterManager = function () {
    function MBClusterManager(dbConnector) {
        _classCallCheck(this, MBClusterManager);

        this.db = dbConnector;

        this.IC213 = new _IC2.default(this.db);
        this.IC222 = new _IC4.default(this.db);
        this.IC204 = new _IC6.default(this.db);
        this.IC172 = new _IC8.default(this.db);
        this.KI221 = new _KI2.default(this.db);
        this.IC177 = new _IC10.default(this.db);
        this.KI211 = new _KI4.default(this.db);
        this.KI164 = new _KI6.default(this.db);
        this.KI203 = new _KI8.default(this.db);
        this.IC907 = new _IC12.default(this.db);

        this.createTokenId = this.createTokenId.bind(this);
        this.tokenAdd = this.tokenAdd.bind(this);
    }

    _createClass(MBClusterManager, [{
        key: "createTokenId",
        value: function createTokenId(hwid, swid) {
            return (0, _md2.default)(hwid + swid);
        }
    }, {
        key: "tokenCheck",
        value: function tokenCheck(userid, cid, sku, hwid, swid, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenCheck(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, (0, _md2.default)(cid + sku + hwid + swid), sku] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }, {
        key: "tokenAdd",
        value: function tokenAdd(userid, cid, sku, hwid, swid, state, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenAdd(?,?,?,?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, (0, _md2.default)(cid + sku + hwid + swid), cid, sku, hwid, swid, state] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }, {
        key: "tokenConfirm",
        value: function tokenConfirm(userid, cid, sku, hwid, swid, state, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenConfirm(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, (0, _md2.default)(cid + sku + hwid + swid), state] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }]);

    return MBClusterManager;
}();

exports.default = MBClusterManager;