"use strict";
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _moment = require("moment");

var _moment2 = _interopRequireDefault(_moment);

var _axios = require("axios");

var _axios2 = _interopRequireDefault(_axios);

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

var _mbClusterManagerBase = require("../mbClusterManagerBase");

var _mbClusterManagerBase2 = _interopRequireDefault(_mbClusterManagerBase);

var _utils = require("../../utils");

var _utils2 = _interopRequireDefault(_utils);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return call && (typeof call === "object" || typeof call === "function") ? call : self; }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function, not " + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }

var SPC213 = function (_MBClusterManagerBase) {
    _inherits(SPC213, _MBClusterManagerBase);

    function SPC213(dbConnector) {
        _classCallCheck(this, SPC213);

        return _possibleConstructorReturn(this, (SPC213.__proto__ || Object.getPrototypeOf(SPC213)).call(this, dbConnector));
    }

    _createClass(SPC213, [{
        key: "DisableFirewall",
        value: function DisableFirewall() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"init\"}," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level61\"], \"reply\": [] }" + "        ]" + "}";

            return CMDResponse;
        }
    }]);

    return SPC213;
}(_mbClusterManagerBase2.default);

exports.default = SPC213;