"use strict"
/*************************
    Async DB Connector
    Coded by <PERSON><PERSON>
**************************/

import mysql from "mysql";
import async from "async";

class DBConnector {
    constructor () {
        this.pool = mysql.createPool({
            connectionLimit : 100,
            host     : '*************',
            user     : 'xentry',
            password : 'Xy2020Xp!',
            database : 'xentry',
            multipleStatements: true,
            debug    :  false
        });  
    }

    QueryExec(SQLquery,callback) {
        const self = this;

        async.waterfall([
                function(callback) {
                    self.pool.getConnection(function(err,connection){
                    if(err) {
                        callback(true);
                    } else {
                        callback(null,connection);
                    }
                    });
                },
                function(connection,callback) {
                    callback(null,connection,SQLquery);
                },
                function(connection,SQLquery,callback) {
                    try {
                        connection.query(SQLquery.query, SQLquery.params ,function(err,rows){                        
                            connection.release();
                            if(!err) {   
                                callback((typeof(rows.length)==='undefined' || rows.length === 0) && rows.affectedRows === 0 ? false : rows);
                            } else {
                                callback(true);
                            }
                        });    
                    } catch (e) {
                        callback(false);
                    }
                }
            ],
            function(result){
                if(typeof(result) === "boolean" && result === true) {
                    callback(null);
                } else {
                    callback(result);
                }
            }
        );
    }    
    
}
export default DBConnector;
