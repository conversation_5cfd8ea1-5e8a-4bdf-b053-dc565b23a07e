"use strict";
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _moment = require("moment");

var _moment2 = _interopRequireDefault(_moment);

var _axios = require("axios");

var _axios2 = _interopRequireDefault(_axios);

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

var _mbClusterManagerBase = require("../mbClusterManagerBase");

var _mbClusterManagerBase2 = _interopRequireDefault(_mbClusterManagerBase);

var _utils = require("../../utils");

var _utils2 = _interopRequireDefault(_utils);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return call && (typeof call === "object" || typeof call === "function") ? call : self; }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function, not " + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }

var KI164 = function (_MBClusterManagerBase) {
    _inherits(KI164, _MBClusterManagerBase);

    function KI164(dbConnector) {
        _classCallCheck(this, KI164);

        return _possibleConstructorReturn(this, (KI164.__proto__ || Object.getPrototypeOf(KI164)).call(this, dbConnector));
    }

    _createClass(KI164, [{
        key: "AMGEnable",
        value: function AMGEnable() {
            var CMDResponse = {
                workflow: [{ proc: "clear" }, { proc: "hardreset" }, { proc: "sleep", param: 3000 }, { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] }, { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] }, { proc: "sgkt", param: ["levelver"], reply: [] }, // get software version of ic so we know what magic number to use
                { proc: "sgkt", param: ["level10"], reply: [] }, // do seedkey, but nobody knows why it is called "level10"
                { proc: "uds", param: [0x21, 0x61], reply: [0x61, 0x61, 0x07] }, { proc: "uds", param: [0x3D, 0x39, 0x06, 0x1F, 0x01, 0x1F], reply: [0x7D, 0x39] }, { proc: "hardreset" }]
            };
            return JSON.stringify(CMDResponse);
        }
    }, {
        key: "AMGDisable",
        value: function AMGDisable() {
            var CMDResponse = {
                workflow: [{ proc: "clear" }, { proc: "hardreset" }, { proc: "sleep", param: 3000 }, { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] }, { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] }, { proc: "sgkt", param: ["levelver"], reply: [] }, // get software version of ic so we know what magic number to use
                { proc: "sgkt", param: ["level10"], reply: [] }, // do seedkey, but nobody knows why it is called "level10"
                { proc: "uds", param: [0x21, 0x61], reply: [0x61, 0x61, 0x07] }, { proc: "uds", param: [0x3D, 0x39, 0x06, 0x1F, 0x01, 0x00], reply: [0x7D, 0x39] }, { proc: "hardreset" }]
            };
            return JSON.stringify(CMDResponse);
        }
    }, {
        key: "Virgin",
        value: function Virgin() {
            var CMDResponse = {
                workflow: [{ proc: "clear" }, { proc: "hardreset" }, { proc: "sleep", param: 3000 }, { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] }, { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] }, { proc: "sgkt", param: ["levelver"], reply: [] }, // get software version of ic so we know what magic number to use
                { proc: "sgkt", param: ["level10"], reply: [] }, // do seedkey, but nobody knows why it is called "level10"
                { proc: "uds", param: [0x21, 0x61], reply: [0x61, 0x61, 0x07] }, { proc: "uds", param: [0x3D, 0x39, 0x00, 0x00, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x39] }, { proc: "uds", param: [0x3D, 0x39, 0x00, 0x10, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x39] }, { proc: "uds", param: [0x3D, 0x39, 0x00, 0x20, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x39] }, { proc: "uds", param: [0x3D, 0x39, 0x00, 0x30, 0x10, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF], reply: [0x7D, 0x39] }, { proc: "uds", param: [0x3D, 0x39, 0x00, 0x40, 0x10, 0xFF, 0xFF, 0xFF, 0xFE, 0xFF, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00], reply: [0x7D, 0x39] }, { proc: "uds", param: [0x3D, 0x39, 0x00, 0x50, 0x0E, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], reply: [0x7D, 0x39] }, { proc: "hardreset" }]
            };
            return JSON.stringify(CMDResponse);
        }
    }, {
        key: "EpromEdit",
        value: function EpromEdit() {
            var CMDResponse = {
                workflow: [{ proc: "clear" }, { proc: "hardreset" }, { proc: "sleep", param: 3000 }, { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] }, { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] }, { proc: "sgkt", param: ["levelver"], reply: [] }, // get software version of ic so we know what magic number to use
                { proc: "sgkt", param: ["level10"], reply: [] }, // do seedkey, but nobody knows why it is called "level10"
                { proc: "uds", param: [0x21, 0x61], reply: [0x61, 0x61, 0x07] }]
            };
            return JSON.stringify(CMDResponse);
        }
    }, {
        key: "Unlock",
        value: function Unlock() {
            var CMDResponse = {
                workflow: [{ proc: "clear" }, { proc: "hardreset" }, { proc: "sleep", param: 3000 }, { proc: "uds", param: [0x10, 0x92], reply: [0x50, 0x92] }, { proc: "uds", param: [0x10, 0xF0], reply: [0x50, 0xF0] }, { proc: "sgkt", param: ["levelver"], reply: [] }, // get software version of ic so we know what magic number to use
                { proc: "sgkt", param: ["level10"], reply: [] }, // do seedkey, but nobody knows why it is called "level10"
                { proc: "uds", param: [0x21, 0x61], reply: [0x61, 0x61, 0x07] }]
            };
            return JSON.stringify(CMDResponse);
        }
    }]);

    return KI164;
}(_mbClusterManagerBase2.default);

exports.default = KI164;