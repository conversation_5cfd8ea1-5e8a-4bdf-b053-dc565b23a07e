"use strict"
/*************************
    MB Variant Backup
    Coded by <PERSON><PERSON>
**************************/
import md5 from "md5";

class MBAntiTheftCode {
    constructor (dbConnector) {
        this.db = dbConnector;
        
        this.createTokenId = this.createTokenId.bind(this); 
        this.get = this.get.bind(this);
        this.add = this.add.bind(this);
        this.generateTheftCode = this.generateTheftCode.bind(this);
        this.tokenAdd = this.tokenAdd.bind(this);
        this.tokenCheck = this.tokenCheck.bind(this);
        this.tokenConfirm = this.tokenConfirm.bind(this);
    }    
    
    createTokenId(serial) {        
        return serial;
    }

    add(serial,callback) {
        let _this = this;        
        let SQLquery = { query: "CALL antitheftcodeAdd(?);" , params: [ serial ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[0]);
        });    
    }
    
    get(serial,callback) {
        let _this = this;
        let SQLquery = { query: "CALL antitheftcodeGet(?);" , params: [ serial ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[0]);
        });    
    }

    generateTheftCode(serial,callback) {   
        let resultFromGen = {
            vinNumber: "",
            antiTheft: "",
            serial: "",
            history: 0,
            found: 0,
            state: 0,
            error: ""
        }
        
        this.get(serial,(dbresult)=>{            
            if (dbresult!=null && dbresult.length!=1) {
                // Added to DB for Generation Code
                this.add(serial, (dbresult)=>{
                    resultFromGen.vinNumber = dbresult[0].vin;
                    resultFromGen.serial = dbresult[0].serial;
                    resultFromGen.antiTheft = dbresult[0].theftcode;
                    resultFromGen.history = dbresult[0].history;
                    resultFromGen.found = 0;                    
                    resultFromGen.state = dbresult[0].state;
                    callback(resultFromGen);
                });   
            } else {
                // Send Existing Data from DB
                if (dbresult[0]!=null) {
                    resultFromGen.vinNumber = dbresult[0].vin;
                    resultFromGen.serial = dbresult[0].serial;
                    resultFromGen.antiTheft = dbresult[0].theftcode;
                    resultFromGen.history = dbresult[0].history;
                    resultFromGen.found = dbresult[0].found;                    
                    resultFromGen.state = dbresult[0].state;
                    if (dbresult[0].found==3) {
                        resultFromGen.error = "Incorrect Serial";
                    }
                }
                callback(resultFromGen);
            }
        });        
    }

    tokenCheck(userid,cid,sku,callback) {
        let _this = this;        
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenCheck(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, cid, sku ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });    
    }
    
    tokenAdd(userid,cid,sku,theftcode,state,callback) {
        let _this = this;
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenAdd(?,?,?,?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, cid, cid, sku, cid, theftcode, state ] };                
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });    
    }

    tokenConfirm(userid,cid,state,callback) {
        let _this = this;
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenConfirm(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, cid, state ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });   
    }      
    
}
export default MBAntiTheftCode;
