"use strict";
/*************************
    MB Variant Backup
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var MBVINDecoder = function () {
    function MBVINDecoder(dbConnector) {
        _classCallCheck(this, MBVINDecoder);

        this.db = dbConnector;

        this.createTokenId = this.createTokenId.bind(this);
        this.get = this.get.bind(this);
        this.add = this.add.bind(this);
        this.vinDecode = this.vinDecode.bind(this);
        this.tokenAdd = this.tokenAdd.bind(this);
        this.tokenCheck = this.tokenCheck.bind(this);
        this.tokenConfirm = this.tokenConfirm.bind(this);
    }

    _createClass(MBVINDecoder, [{
        key: "createTokenId",
        value: function createTokenId(serial) {
            return serial;
        }
    }, {
        key: "add",
        value: function add(vin, callback) {
            var _this = this;
            var SQLquery = { query: "CALL sacodeAdd(?);", params: [vin] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[0]);
            });
        }
    }, {
        key: "get",
        value: function get(vin, callback) {
            var _this = this;
            var SQLquery = { query: "CALL sacodeGet(?);", params: [vin] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[0]);
            });
        }
    }, {
        key: "vinDecode",
        value: function vinDecode(serial, callback) {
            var _this2 = this;

            var resultFromGen = {
                brand: "",
                model: "",
                bodytype: "",
                production: "",
                bodycolor: "",
                interiorcolor: "",
                engineno: "",
                transmissionno: "",
                sacodes: [],
                swids: [],
                found: 0,
                state: 0,
                error: ""
            };

            this.get(serial, function (dbresult) {
                if (dbresult != null && dbresult.length != 1) {
                    // Added to DB for Generation Code
                    _this2.add(serial, function (dbresult) {
                        resultFromGen.brand = dbresult[0].brand;
                        resultFromGen.model = dbresult[0].model;
                        resultFromGen.bodytype = dbresult[0].bodytype;
                        resultFromGen.production = dbresult[0].production;
                        resultFromGen.bodycolor = dbresult[0].bodycolor;
                        resultFromGen.interiorcolor = dbresult[0].interiorcolor;
                        resultFromGen.engineno = dbresult[0].engineno;
                        resultFromGen.transmissionno = dbresult[0].transmissionno;
                        resultFromGen.sacodes = dbresult[0].sacodes;
                        resultFromGen.swids = dbresult[0].swids;
                        resultFromGen.found = 0;
                        resultFromGen.state = dbresult[0].state;
                        callback(resultFromGen);
                    });
                } else {
                    // Send Existing Data from DB
                    if (dbresult[0] != null) {
                        resultFromGen.brand = dbresult[0].brand;
                        resultFromGen.model = dbresult[0].model;
                        resultFromGen.bodytype = dbresult[0].bodytype;
                        resultFromGen.production = dbresult[0].production;
                        resultFromGen.bodycolor = dbresult[0].bodycolor;
                        resultFromGen.interiorcolor = dbresult[0].interiorcolor;
                        resultFromGen.engineno = dbresult[0].engineno;
                        resultFromGen.transmissionno = dbresult[0].transmissionno;
                        resultFromGen.sacodes = dbresult[0].sacodes;
                        resultFromGen.swids = dbresult[0].swids;
                        resultFromGen.found = 1;
                        resultFromGen.state = dbresult[0].state;
                    }
                    callback(resultFromGen);
                }
            });
        }
    }, {
        key: "tokenCheck",
        value: function tokenCheck(userid, cid, sku, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenCheck(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, cid, sku] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }, {
        key: "tokenAdd",
        value: function tokenAdd(userid, cid, sku, theftcode, state, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenAdd(?,?,?,?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, cid, cid, sku, cid, theftcode, state] };
            console.log(SQLquery);
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }, {
        key: "tokenConfirm",
        value: function tokenConfirm(userid, cid, state, callback) {
            var _this = this;
            var SQLquery = { query: "Set @ReturnValue=0; CALL tokenConfirm(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue", params: [userid, cid, state] };
            _this.db.QueryExec(SQLquery, function (result) {
                callback(result[2][0]["ReturnValue"]);
            });
        }
    }]);

    return MBVINDecoder;
}();

exports.default = MBVINDecoder;