"use strict";
/*************************
    Releases Class
    Coded by <PERSON><PERSON>
**************************/

Object.defineProperty(exports, "__esModule", {
    value: true
});

var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

var _moment = require("moment");

var _moment2 = _interopRequireDefault(_moment);

var _axios = require("axios");

var _axios2 = _interopRequireDefault(_axios);

var _md = require("md5");

var _md2 = _interopRequireDefault(_md);

var _mbClusterManagerBase = require("../mbClusterManagerBase");

var _mbClusterManagerBase2 = _interopRequireDefault(_mbClusterManagerBase);

var _utils = require("../../utils");

var _utils2 = _interopRequireDefault(_utils);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return call && (typeof call === "object" || typeof call === "function") ? call : self; }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function, not " + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }

var KI221 = function (_MBClusterManagerBase) {
    _inherits(KI221, _MBClusterManagerBase);

    function KI221(dbConnector) {
        _classCallCheck(this, KI221);

        return _possibleConstructorReturn(this, (KI221.__proto__ || Object.getPrototypeOf(KI221)).call(this, dbConnector));
    }

    _createClass(KI221, [{
        key: "PreAMGEnable320",
        value: function PreAMGEnable320() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level01\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"levelfd\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x01 + "," + 0xEB + "," + 0x02 + "," + 0x1E + "," + 0x5F + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x03 + "," + 0xB0 + "," + 0x02 + "," + 0x30 + "," + 0x85 + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "PreAMGEnable360",
        value: function PreAMGEnable360() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level01\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"levelfd\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x01 + "," + 0xEB + "," + 0x02 + "," + 0x1F + "," + 0x5F + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x03 + "," + 0xB0 + "," + 0x02 + "," + 0x30 + "," + 0x85 + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "FLAMGEnable320",
        value: function FLAMGEnable320() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level01\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"levelfd\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x01 + "," + 0xEB + "," + 0x02 + "," + 0x7E + "," + 0x5F + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x03 + "," + 0xB0 + "," + 0x02 + "," + 0x08 + "," + 0x85 + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "FLAMGEnable360",
        value: function FLAMGEnable360() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level01\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"levelfd\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x01 + "," + 0xEB + "," + 0x02 + "," + 0x7F + "," + 0x5F + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x03 + "," + 0xB0 + "," + 0x02 + "," + 0x08 + "," + 0x85 + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "BrabusEnable320",
        value: function BrabusEnable320() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level01\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"levelfd\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x03 + "," + 0xB1 + "," + 0x01 + "," + 0x89 + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x01 + "," + 0xAA + "," + 0x01 + "," + 0x04 + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "BrabusEnable360",
        value: function BrabusEnable360() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level01\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"levelfd\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x03 + "," + 0xB1 + "," + 0x01 + "," + 0x89 + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x01 + "," + 0xAA + "," + 0x01 + "," + 0x34 + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "AMGDisable",
        value: function AMGDisable() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level01\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"levelfd\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x03 + "," + 0xB1 + "," + 0x01 + "," + 0x81 + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x01 + "," + 0xEB + "," + 0x02 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x03 + "," + 0xB0 + "," + 0x02 + "," + 0x08 + "," + 0x81 + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x01 + "," + 0xAA + "," + 0x01 + "," + 0x04 + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "Virgin",
        value: function Virgin() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"uds\", \"param\": [" + 0x1A + "," + 0x87 + "], \"reply\": [" + 0x5A + "," + 0x87 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x85 + "], \"reply\": [" + 0x50 + "," + 0x85 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x1A + "," + 0x9E + "], \"reply\": [" + 0x5A + "," + 0x9E + "] }," + "            { \"proc\": \"sgkt\", \"param\": [\"level05\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3B + "," + 0x9A + "," + 0x01 + "," + 0x01 + "," + 0x04 + "," + 0x10 + "," + 0x01 + "," + 0x20 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7B + "," + 0x9A + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x34 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x01 + "," + 0x80 + "], \"reply\": [" + 0x74 + "," + 0x0F + "," + 0xFF + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x36 + "," + 0x01 + "," + 0x00 + "," + 0x85 + "," + 0x29 + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x14 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0xA0 + "," + 0xD4 + "," + 0x9F + "," + 0x2E + "," + 0xF4 + "," + 0xD4 + "," + 0xA7 + "," + 0x2E + "," + 0xF4 + "," + 0xD4 + "," + 0xAF + "," + 0x2E + "," + 0xF4 + "," + 0xD4 + "," + 0x1F + "," + 0x0E + "," + 0xF4 + "," + 0xD4 + "," + 0x27 + "," + 0x0E + "," + 0xF4 + "," + 0xD4 + "," + 0xAF + "," + 0x0E + "," + 0xF4 + "," + 0x20 + "," + 0x46 + "," + 0x00 + "," + 0x00 + "," + 0x20 + "," + 0x4E + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x98 + "," + 0x39 + "," + 0x06 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x5F + "," + 0xCA + "," + 0xFA + "," + 0xFD + "," + 0x80 + "," + 0xCF + "," + 0x88 + "," + 0x00 + "," + 0x20 + "," + 0x26 + "," + 0xA0 + "," + 0x00 + "," + 0x80 + "," + 0xCF + "," + 0xC2 + "," + 0x00 + "," + 0x13 + "," + 0x20 + "," + 0x80 + "," + 0xCF + "," + 0xBC + "," + 0x00 + "," + 0x08 + "," + 0x8A + "," + 0x08 + "," + 0x20 + "," + 0x80 + "," + 0xCF + "," + 0xB4 + "," + 0x00 + "," + 0x09 + "," + 0x20 + "," + 0x80 + "," + 0xCF + "," + 0xAE + "," + 0x00 + "," + 0x5F + "," + 0x8A + "," + 0x9A + "," + 0xFD + "," + 0x80 + "," + 0xCF + "," + 0x82 + "," + 0x00 + "," + 0x20 + "," + 0xD6 + "," + 0x16 + "," + 0x00 + "," + 0x39 + "," + 0x06 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x5F + "," + 0xCA + "," + 0xFA + "," + 0xFD + "," + 0x5F + "," + 0xD2 + "," + 0xAA + "," + 0xFD + "," + 0x13 + "," + 0x9E + "," + 0x10 + "," + 0x00 + "," + 0x20 + "," + 0x0E + "," + 0x20 + "," + 0x00 + "," + 0xE1 + "," + 0x99 + "," + 0xEA + "," + 0xDD + "," + 0x00 + "," + 0x98 + "," + 0x39 + "," + 0x06 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x5F + "," + 0xCA + "," + 0xFA + "," + 0xFD + "," + 0x80 + "," + 0xCF + "," + 0x36 + "," + 0x00 + "," + 0x20 + "," + 0x26 + "," + 0xA0 + "," + 0x00 + "," + 0x80 + "," + 0xCF + "," + 0x70 + "," + 0x00 + "," + 0x13 + "," + 0x20 + "," + 0x80 + "," + 0xCF + "," + 0x6A + "," + 0x00 + "," + 0x80 + "," + 0xCF + "," + 0x42 + "," + 0x00 + "," + 0x80 + "," + 0xCF + "," + 0x20 + "," + 0x00 + "," + 0x20 + "," + 0x26 + "," + 0xA1 + "," + 0x00 + "," + 0x80 + "," + 0xCF + "," + 0x5A + "," + 0x00 + "," + 0x80 + "," + 0xCF + "," + 0xA2 + "," + 0x00 + "," + 0x80 + "," + 0xCF + "," + 0x2E + "," + 0x00 + "," + 0x41 + "," + 0x9A + "," + 0x20 + "," + 0x0E + "," + 0x20 + "," + 0x00 + "," + 0xE1 + "," + 0x99 + "," + 0xAA + "," + 0xE5 + "," + 0x60 + "," + 0x00 + "," + 0xD4 + "," + 0x1F + "," + 0x0E + "," + 0xF4 + "," + 0xD4 + "," + 0x27 + "," + 0x0E + "," + 0xF4 + "," + 0x80 + "," + 0xFF + "," + 0x30 + "," + 0x00 + "," + 0xD4 + "," + 0x9F + "," + 0x0E + "," + 0xF4 + "," + 0x80 + "," + 0xFF + "," + 0x28 + "," + 0x00 + "," + 0xD4 + "," + 0xA7 + "," + 0x0E + "," + 0xF4 + "," + 0x80 + "," + 0xFF + "," + 0x20 + "," + 0x00 + "," + 0x79 + "," + 0x00 + "," + 0xD4 + "," + 0x9F + "," + 0x0E + "," + 0xF4 + "," + 0xD4 + "," + 0xA7 + "," + 0x0E + "," + 0xF4 + "," + 0x80 + "," + 0xFF + "," + 0x12 + "," + 0x00 + "," + 0xD4 + "," + 0x27 + "," + 0x0E + "," + 0xF4 + "," + 0x80 + "," + 0xFF + "," + 0x0A + "," + 0x00 + "," + 0xD4 + "," + 0x1F + "," + 0x0E + "," + 0xF4 + "," + 0x79 + "," + 0x00 + "," + 0x20 + "," + 0xAE + "," + 0xFF + "," + 0x00 + "," + 0x5F + "," + 0xAA + "," + 0xFA + "," + 0xFD + "," + 0x7F + "," + 0x00 + "," + 0x08 + "," + 0x92 + "," + 0xC4 + "," + 0x2E + "," + 0x80 + "," + 0x00 + "," + 0xD2 + "," + 0x05 + "," + 0xD4 + "," + 0x1F + "," + 0x0E + "," + 0xF4 + "," + 0x80 + "," + 0x07 + "," + 0x08 + "," + 0x00 + "," + 0xD4 + "," + 0x9F + "," + 0x0E + "," + 0xF4 + "," + 0xBF + "," + 0xFF + "," + 0xE2 + "," + 0xFF + "," + 0xD4 + "," + 0x27 + "," + 0x0E + "," + 0xF4 + "," + 0xBF + "," + 0xFF + "," + 0xDA + "," + 0xFF + "," + 0xD4 + "," + 0xA7 + "," + 0x0E + "," + 0xF4 + "," + 0xBF + "," + 0xFF + "," + 0xD2 + "," + 0xFF + "," + 0xC1 + "," + 0x22 + "," + 0x5F + "," + 0x92 + "," + 0xBA + "," + 0xED + "," + 0xD4 + "," + 0x1F + "," + 0x2E + "," + 0xF4 + "," + 0xBF + "," + 0xFF + "," + 0xC4 + "," + 0xFF + "," + 0xD4 + "," + 0x27 + "," + 0x0E + "," + 0xF4 + "," + 0xBF + "," + 0xFF + "," + 0xBC + "," + 0xFF + "," + 0xD4 + "," + 0xA7 + "," + 0x0E + "," + 0xF4 + "," + 0xBF + "," + 0xFF + "," + 0xB4 + "," + 0xFF + "," + 0xD4 + "," + 0x9F + "," + 0x2E + "," + 0xF4 + "," + 0x79 + "," + 0x00 + "," + 0xD4 + "," + 0x1F + "," + 0x2E + "," + 0xF4 + "," + 0x09 + "," + 0x92 + "," + 0xBF + "," + 0xFF + "," + 0xA4 + "," + 0xFF + "," + 0xD4 + "," + 0x27 + "," + 0x0E + "," + 0xF4 + "," + 0xBF + "," + 0xFF + "," + 0x9C + "," + 0xFF + "," + 0xD4 + "," + 0xA7 + "," + 0x0E + "," + 0xF4 + "," + 0xBF + "," + 0xFF + "," + 0x94 + "," + 0xFF + "," + 0xC1 + "," + 0x22 + "," + 0x5F + "," + 0x92 + "," + 0xCA + "," + 0xF5 + "," + 0xD4 + "," + 0x9F + "," + 0x2E + "," + 0xF4 + "," + 0x79 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x76 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x37 + "], \"reply\": [" + 0x77 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x31 + "," + 0xE0 + "], \"reply\": [] }," + // like this?yea
            "            { \"proc\": \"sleep\", \"param\": 3000}" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "Virgin2",
        value: function Virgin2(swid) {

            var UnlockAddress = "";

            switch (swid) {
                case "2214422021":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA0 + "," + 0xF8 + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0xFF + "," + 0xA0 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA1 + "," + 0xC8 + "," + 0x04 + "," + 0xF8 + "," + 0xA0 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2214422121":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA1 + "," + 0x3C + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0x43 + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA2 + "," + 0x0C + "," + 0x04 + "," + 0x3C + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2214422321":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA1 + "," + 0x70 + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0x77 + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA2 + "," + 0x44 + "," + 0x04 + "," + 0x70 + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2214420821":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA1 + "," + 0x70 + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0x77 + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA2 + "," + 0x44 + "," + 0x04 + "," + 0x70 + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2214423621":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA1 + "," + 0x70 + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0x77 + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA2 + "," + 0x44 + "," + 0x04 + "," + 0x70 + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2214424021":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA1 + "," + 0xC0 + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0xC7 + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA2 + "," + 0x9C + "," + 0x04 + "," + 0xC0 + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2214424621":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA1 + "," + 0xEC + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0xF3 + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA2 + "," + 0xC8 + "," + 0x04 + "," + 0xEC + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2214424721":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA1 + "," + 0xFC + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0x03 + "," + 0xA2 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA2 + "," + 0xD8 + "," + 0x04 + "," + 0xFC + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2214425021":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA1 + "," + 0xFC + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0x03 + "," + 0xA2 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA2 + "," + 0xD8 + "," + 0x04 + "," + 0xFC + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2219022402":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA4 + "," + 0x58 + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0x5F + "," + 0xA4 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA5 + "," + 0x38 + "," + 0x04 + "," + 0x58 + "," + 0xA4 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2219023000":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA4 + "," + 0x9C + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0xA3 + "," + 0xA4 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA5 + "," + 0x84 + "," + 0x04 + "," + 0x9C + "," + 0xA4 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2219024702":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA4 + "," + 0xA8 + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0xAF + "," + 0xA4 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA5 + "," + 0x90 + "," + 0x04 + "," + 0xA8 + "," + 0xA4 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2219024902":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA1 + "," + 0xFC + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0x03 + "," + 0xA2 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA2 + "," + 0xD8 + "," + 0x04 + "," + 0xFC + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2219025301":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA4 + "," + 0x54 + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0x5B + "," + 0xA4 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA5 + "," + 0x34 + "," + 0x04 + "," + 0x54 + "," + 0xA4 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2219028901":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA4 + "," + 0xA8 + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0xAF + "," + 0xA4 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA5 + "," + 0x90 + "," + 0x04 + "," + 0xA8 + "," + 0xA4 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
                case "2219029600":
                    UnlockAddress = "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA4 + "," + 0x54 + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0x5B + "," + 0xA4 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    UnlockAddress += "{ \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA5 + "," + 0x34 + "," + 0x04 + "," + 0x54 + "," + 0xA4 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] },";
                    break;
            }

            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level01\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"levelfd\"], \"reply\": [] }," + UnlockAddress +
            //"            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA1 + "," + 0x70 + "," + 0x10 + "," + 0x28 + "," + 0x00 + "," + 0x01 + "," + 0xC0 + "," + 0x77 + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 +"], \"reply\": [" + 0x7D + "," + 0x00 + "] }," +
            //"            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x00 + "," + 0xA2 + "," + 0x44 + "," + 0x04 + "," + 0x70 + "," + 0xA1 + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x00 + "] }," +
            "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x81 + "], \"reply\": [" + 0x50 + "," + 0x81 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"sgkt\", \"param\": [\"level01\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"levelfd\"], \"reply\": [] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x23 + "," + 0x01 + "," + 0x00 + "," + 0x28 + "," + 0x01 + "], \"reply\": [" + 0x63 + "," + 0xFF + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x00 + "," + 0x00 + "," + 0x08 + "," + 0x00 + "," + 0x00 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x00 + "," + 0x08 + "," + 0x08 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x00 + "," + 0x10 + "," + 0x08 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x00 + "," + 0x18 + "," + 0x08 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x00 + "," + 0x20 + "," + 0x08 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x00 + "," + 0x28 + "," + 0x08 + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "," + 0xFF + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3B + "," + 0x3D + "," + 0x00 + "," + 0x00 + "], \"reply\": [" + 0x7B + "," + 0x3D + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x3D + "," + 0x01 + "," + 0x00 + "," + 0x28 + "," + 0x01 + "," + 0x07 + "], \"reply\": [" + 0x7D + "," + 0x01 + "] }," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"sleep\", \"param\": 3000}" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "EpromEdit",
        value: function EpromEdit() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level01\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"levelfd\"], \"reply\": [] }" + "        ]" + "}";
            return CMDResponse;
        }
    }, {
        key: "Unlock",
        value: function Unlock() {
            var CMDResponse = "{" + "    \"workflow\":" + "        [" + "            { \"proc\": \"clear\"}," + "            { \"proc\": \"hardreset\" }," + "            { \"proc\": \"sleep\", \"param\": 2000}," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"uds\", \"param\": [" + 0x10 + "," + 0x92 + "], \"reply\": [" + 0x50 + "," + 0x92 + "] }," + "            { \"proc\": \"getswid\"}," + "            { \"proc\": \"sgkt\", \"param\": [\"level01\"], \"reply\": [] }," + "            { \"proc\": \"sleep\", \"param\": 1000}," + "            { \"proc\": \"sgkt\", \"param\": [\"levelfd\"], \"reply\": [] }" + "        ]" + "}";
            return CMDResponse;
        }
    }]);

    return KI221;
}(_mbClusterManagerBase2.default);

exports.default = KI221;