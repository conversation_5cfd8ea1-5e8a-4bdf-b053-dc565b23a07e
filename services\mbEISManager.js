"use strict"
/*************************
    MB Services Classes
    Coded by <PERSON><PERSON>
**************************/
import md5 from "md5";
import EZS213 from "./eis/EZS213";
import EZS167 from "./eis/EZS167";
import EIS447 from "./eis/EIS447";

class MBEISManager {
    constructor (dbConnector) {
        this.db = dbConnector;

        this.EZS213 = new EZS213(this.db);      
        this.EZS167 = new EZS167(this.db);      
        this.EIS447 = new EIS447(this.db);      
        
        this.createTokenId = this.createTokenId.bind(this); 
        this.tokenAdd = this.tokenAdd.bind(this); 
    }    
    
    createTokenId(hwid,swid) {        
        return md5(hwid+swid);
    }

    tokenCheck(userid,cid,sku,hwid,swid,callback) {
        let _this = this;        
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenCheck(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, md5(cid+sku+hwid+swid), sku ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });    
    }
    
    tokenAdd(userid,cid,sku,hwid,swid,state,callback) {
        let _this = this;
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenAdd(?,?,?,?,?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, md5(cid+sku+hwid+swid), cid, sku, hwid, swid, state ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });    
    }

    tokenConfirm(userid,cid,sku,hwid,swid,state,callback) {
        let _this = this;
        let SQLquery = { query: "Set @ReturnValue=0; CALL tokenConfirm(?,?,?,@ReturnValue); Select @ReturnValue as ReturnValue" , params: [ userid, md5(cid+sku+hwid+swid), state ] };        
        _this.db.QueryExec(SQLquery, (result)=>{  
            callback(result[2][0]["ReturnValue"]);
        });   
    }    
    
}
export default MBEISManager;
