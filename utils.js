"use strict"
/*************************
    Utils
    Coded by <PERSON><PERSON>
**************************/

const aes256 = require('aes256');
const md5 = require('md5');
const { base64encode, base64decode } = require('nodejs-base64');
import CryptoJS from 'crypto-js';

/**
 * An API to allow for greatly simplified AES-256 encryption and decryption using a passphrase of
 * any length plus a random Initialization Vector.
 * @exports aes256
 * @public
 */
var utils = {

    encodeAPI: function(companyid,bankid,api) {
        return aes256.encrypt(md5('ny'+companyid+''+bankid), api);
    },

    decodeAPI: function(companyid,bankid,encrypted) {
        return aes256.decrypt(md5('ny'+companyid+''+bankid), encrypted);
    },

    encrypt: (plaintext) => {
        var key = CryptoJS.enc.Utf8.parse('Pw7dxZs8glfor9tnc2VnsdgH1etkfjcv');
        var iv = CryptoJS.enc.Utf8.parse('bF1lc3sjd8zpt9cx');
        var message = CryptoJS.enc.Utf8.parse(plaintext);
        var encrypted = CryptoJS.AES.encrypt(message, key,        
        {
            keySize: 256,
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });        
        return encrypted.toString();
    },

    decrypt: (ciphertext) => {
        var key = CryptoJS.enc.Utf8.parse('Pw7dxZs8glfor9tnc2VnsdgH1etkfjcv');
        var iv = CryptoJS.enc.Utf8.parse('bF1lc3sjd8zpt9cx');
        var encryptedPassword = EncodingUtil.base64Decode(ciphertext);
        var decryptedPassword = CryptoJS.AES.decrypt(encryptedPassword, key, iv);
        return decryptedPassword.toString();
    }    
};

//
// Export the API
//
module.exports = utils;
